//go:cgo_ldflag "-O2"
//go:cgo_ldflag "-g"
// Code generated by cmd/cgo; DO NOT EDIT.

package user

import "unsafe"

import "syscall"

import _cgopackage "runtime/cgo"

type _ _cgopackage.Incomplete
var _ syscall.Errno
func _Cgo_ptr(ptr unsafe.Pointer) unsafe.Pointer { return ptr }

//go:linkname _Cgo_always_false runtime.cgoAlwaysFalse
var _Cgo_always_false bool
//go:linkname _Cgo_use runtime.cgoUse
func _Cgo_use(interface{})
//go:linkname _Cgo_no_callback runtime.cgoNoCallback
func _Cgo_no_callback(bool)
type _Ctype__GoString_ string

type _Ctype___gid_t = _Ctype_uint

type _Ctype___uid_t = _Ctype_uint

type _Ctype_char int8

type _Ctype_gid_t = _Ctype___gid_t

type _Ctype_int int32

type _Ctype_intgo = _Ctype_ptrdiff_t

type _Ctype_long int64

type _Ctype_ptrdiff_t = _Ctype_long

type _Ctype_size_t = _Ctype_ulong

type _Ctype_struct_group struct {
	gr_name		*_Ctype_char
	gr_passwd	*_Ctype_char
	gr_gid		_Ctype___gid_t
	gr_mem		**_Ctype_char
}

type _Ctype_struct_passwd struct {
	pw_name		*_Ctype_char
	pw_passwd	*_Ctype_char
	pw_uid		_Ctype___uid_t
	pw_gid		_Ctype___gid_t
	pw_gecos	*_Ctype_char
	pw_dir		*_Ctype_char
	pw_shell	*_Ctype_char
}

type _Ctype_uid_t = _Ctype___uid_t

type _Ctype_uint uint32

type _Ctype_ulong uint64

type _Ctype_void [0]byte

//go:linkname _cgo_runtime_cgocall runtime.cgocall
func _cgo_runtime_cgocall(unsafe.Pointer, uintptr) int32

//go:linkname _cgoCheckPointer runtime.cgoCheckPointer
//go:noescape
func _cgoCheckPointer(interface{}, interface{})

//go:linkname _cgoCheckResult runtime.cgoCheckResult
//go:noescape
func _cgoCheckResult(interface{})
const _Ciconst__SC_GETGR_R_SIZE_MAX = 0x45
const _Ciconst__SC_GETPW_R_SIZE_MAX = 0x46


//go:linkname _cgo_runtime_gostring runtime.gostring
func _cgo_runtime_gostring(*_Ctype_char) string

// GoString converts the C string p into a Go string.
func _Cfunc_GoString(p *_Ctype_char) string {
	return _cgo_runtime_gostring(p)
}
//go:cgo_import_static _cgo_6f668e16310a_Cfunc_mygetgrgid_r
//go:linkname __cgofn__cgo_6f668e16310a_Cfunc_mygetgrgid_r _cgo_6f668e16310a_Cfunc_mygetgrgid_r
var __cgofn__cgo_6f668e16310a_Cfunc_mygetgrgid_r byte
var _cgo_6f668e16310a_Cfunc_mygetgrgid_r = unsafe.Pointer(&__cgofn__cgo_6f668e16310a_Cfunc_mygetgrgid_r)

//go:cgo_unsafe_args
func _Cfunc_mygetgrgid_r(p0 _Ctype_int, p1 *_Ctype_char, p2 _Ctype_size_t, p3 *_Ctype_int, p4 *_Ctype_int) (r1 _Ctype_struct_group) {
	_cgo_runtime_cgocall(_cgo_6f668e16310a_Cfunc_mygetgrgid_r, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_6f668e16310a_Cfunc_mygetgrnam_r
//go:linkname __cgofn__cgo_6f668e16310a_Cfunc_mygetgrnam_r _cgo_6f668e16310a_Cfunc_mygetgrnam_r
var __cgofn__cgo_6f668e16310a_Cfunc_mygetgrnam_r byte
var _cgo_6f668e16310a_Cfunc_mygetgrnam_r = unsafe.Pointer(&__cgofn__cgo_6f668e16310a_Cfunc_mygetgrnam_r)

//go:cgo_unsafe_args
func _Cfunc_mygetgrnam_r(p0 *_Ctype_char, p1 *_Ctype_char, p2 _Ctype_size_t, p3 *_Ctype_int, p4 *_Ctype_int) (r1 _Ctype_struct_group) {
	_cgo_runtime_cgocall(_cgo_6f668e16310a_Cfunc_mygetgrnam_r, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_6f668e16310a_Cfunc_mygetgrouplist
//go:linkname __cgofn__cgo_6f668e16310a_Cfunc_mygetgrouplist _cgo_6f668e16310a_Cfunc_mygetgrouplist
var __cgofn__cgo_6f668e16310a_Cfunc_mygetgrouplist byte
var _cgo_6f668e16310a_Cfunc_mygetgrouplist = unsafe.Pointer(&__cgofn__cgo_6f668e16310a_Cfunc_mygetgrouplist)

//go:cgo_unsafe_args
func _Cfunc_mygetgrouplist(p0 *_Ctype_char, p1 _Ctype_gid_t, p2 *_Ctype_gid_t, p3 *_Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_6f668e16310a_Cfunc_mygetgrouplist, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_6f668e16310a_Cfunc_mygetpwnam_r
//go:linkname __cgofn__cgo_6f668e16310a_Cfunc_mygetpwnam_r _cgo_6f668e16310a_Cfunc_mygetpwnam_r
var __cgofn__cgo_6f668e16310a_Cfunc_mygetpwnam_r byte
var _cgo_6f668e16310a_Cfunc_mygetpwnam_r = unsafe.Pointer(&__cgofn__cgo_6f668e16310a_Cfunc_mygetpwnam_r)

//go:cgo_unsafe_args
func _Cfunc_mygetpwnam_r(p0 *_Ctype_char, p1 *_Ctype_char, p2 _Ctype_size_t, p3 *_Ctype_int, p4 *_Ctype_int) (r1 _Ctype_struct_passwd) {
	_cgo_runtime_cgocall(_cgo_6f668e16310a_Cfunc_mygetpwnam_r, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_6f668e16310a_Cfunc_mygetpwuid_r
//go:linkname __cgofn__cgo_6f668e16310a_Cfunc_mygetpwuid_r _cgo_6f668e16310a_Cfunc_mygetpwuid_r
var __cgofn__cgo_6f668e16310a_Cfunc_mygetpwuid_r byte
var _cgo_6f668e16310a_Cfunc_mygetpwuid_r = unsafe.Pointer(&__cgofn__cgo_6f668e16310a_Cfunc_mygetpwuid_r)

//go:cgo_unsafe_args
func _Cfunc_mygetpwuid_r(p0 _Ctype_int, p1 *_Ctype_char, p2 _Ctype_size_t, p3 *_Ctype_int, p4 *_Ctype_int) (r1 _Ctype_struct_passwd) {
	_cgo_runtime_cgocall(_cgo_6f668e16310a_Cfunc_mygetpwuid_r, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_6f668e16310a_Cfunc_sysconf
//go:linkname __cgofn__cgo_6f668e16310a_Cfunc_sysconf _cgo_6f668e16310a_Cfunc_sysconf
var __cgofn__cgo_6f668e16310a_Cfunc_sysconf byte
var _cgo_6f668e16310a_Cfunc_sysconf = unsafe.Pointer(&__cgofn__cgo_6f668e16310a_Cfunc_sysconf)

//go:cgo_unsafe_args
func _Cfunc_sysconf(p0 _Ctype_int) (r1 _Ctype_long) {
	_cgo_runtime_cgocall(_cgo_6f668e16310a_Cfunc_sysconf, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
