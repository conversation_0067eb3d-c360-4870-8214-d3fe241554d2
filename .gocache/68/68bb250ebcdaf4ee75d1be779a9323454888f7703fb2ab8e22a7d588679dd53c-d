// Code generated by cmd/cgo; DO NOT EDIT.

//line /home/<USER>/go/pkg/mod/github.com/mattn/go-sqlite3@v1.14.31/sqlite3_opt_serialize.go:1:1
//go:build !libsqlite3 || sqlite_serialize
// +build !libsqlite3 sqlite_serialize

package sqlite3

/*
#ifndef USE_LIBSQLITE3
#include <sqlite3-binding.h>
#else
#include <sqlite3.h>
#endif
#include <stdlib.h>
#include <stdint.h>
*/
import _ "unsafe"

import (
	"fmt"
	"math"
	"reflect"
	"unsafe"
)

// Serialize returns a byte slice that is a serialization of the database.
//
// See https://www.sqlite.org/c3ref/serialize.html
func (c *SQLiteConn) Serialize(schema string) ([]byte, error) {
	if schema == "" {
		schema = "main"
	}
	var zSchema * /*line :31:15*/_Ctype_char /*line :31:21*/
	zSchema = ( /*line :32:12*/_Cfunc_CString /*line :32:20*/)(schema)
	defer func() func() { _cgo0 := /*line :33:15*/unsafe.Pointer(zSchema); return func() { _cgoCheckPointer(_cgo0, nil); /*line :33:39*/_Cfunc_free(_cgo0); }}()()

	var sz  /*line :35:9*/_Ctype_sqlite3_int64 /*line :35:24*/
	ptr := func() *_Ctype_uchar{ _cgo0 := /*line :36:29*/c.db; var _cgo1 *_Ctype_char = /*line :36:35*/zSchema; var _cgo2 *_Ctype_sqlite3_int64 = /*line :36:44*/&sz; var _cgo3 _Ctype_uint = /*line :36:49*/0; _cgoCheckPointer(_cgo0, nil); return /*line :36:51*/_Cfunc_sqlite3_serialize(_cgo0, _cgo1, _cgo2, _cgo3); }()
	if ptr == nil {
		return nil, fmt.Errorf("serialize failed")
	}
	defer func() func() { _cgo0 := /*line :40:23*/unsafe.Pointer(ptr); return func() { _cgoCheckPointer(_cgo0, nil); /*line :40:43*/_Cfunc_sqlite3_free(_cgo0); }}()()

	if sz >  /*line :42:10*/_Ctype_sqlite3_int64 /*line :42:25*/(math.MaxInt) {
		return nil, fmt.Errorf("serialized database is too large (%d bytes)", sz)
	}

	cBuf := *(*[]byte)(unsafe.Pointer(&reflect.SliceHeader{
		Data: uintptr(unsafe.Pointer(ptr)),
		Len:  int(sz),
		Cap:  int(sz),
	}))

	res := make([]byte, int(sz))
	copy(res, cBuf)
	return res, nil
}

// Deserialize causes the connection to disconnect from the current database and
// then re-open as an in-memory database based on the contents of the byte slice.
//
// See https://www.sqlite.org/c3ref/deserialize.html
func (c *SQLiteConn) Deserialize(b []byte, schema string) error {
	if schema == "" {
		schema = "main"
	}
	var zSchema * /*line :65:15*/_Ctype_char /*line :65:21*/
	zSchema = ( /*line :66:12*/_Cfunc_CString /*line :66:20*/)(schema)
	defer func() func() { _cgo0 := /*line :67:15*/unsafe.Pointer(zSchema); return func() { _cgoCheckPointer(_cgo0, nil); /*line :67:39*/_Cfunc_free(_cgo0); }}()()

	tmpBuf := (* /*line :69:14*/_Ctype_uchar /*line :69:21*/)(( /*line :69:23*/_Cfunc_sqlite3_malloc64 /*line :69:40*/)( /*line :69:42*/_Ctype_sqlite3_uint64 /*line :69:58*/(len(b))))
	cBuf := *(*[]byte)(unsafe.Pointer(&reflect.SliceHeader{
		Data: uintptr(unsafe.Pointer(tmpBuf)),
		Len:  len(b),
		Cap:  len(b),
	}))
	copy(cBuf, b)

	rc := func() _Ctype_int{ _cgo0 := /*line :77:30*/c.db; var _cgo1 *_Ctype_char = /*line :77:36*/zSchema; var _cgo2 *_Ctype_uchar = /*line :77:45*/tmpBuf; var _cgo3 _Ctype_sqlite3_int64 = _Ctype_sqlite3_int64 /*line :77:68*/(len(b)); var _cgo4 _Ctype_sqlite3_int64 = _Ctype_sqlite3_int64 /*line :78:18*/(len(b)); var _cgo5 _Ctype_uint = /*line :78:28*/_Ciconst_SQLITE_DESERIALIZE_FREEONCLOSE /*line :78:60*/; _cgoCheckPointer(_cgo0, nil); return /*line :78:61*/_Cfunc_sqlite3_deserialize(_cgo0, _cgo1, _cgo2, _cgo3, _cgo4, _cgo5); }()
	if rc != ( /*line :79:11*/_Ciconst_SQLITE_OK /*line :79:21*/) {
		return fmt.Errorf("deserialize failed with return %v", rc)
	}
	return nil
}
