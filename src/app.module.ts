import { Module } from '@nestjs/common';
import { SentryModule } from '@sentry/nestjs/setup';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from './db/prisma.module';
import { join } from 'path';
import { AuthModule } from './resources/auth/auth.module';
import { RolesModule } from './resources/roles/roles.module';
import { PermissionsModule } from './resources/permissions/permissions.module';
import { EmployeeRolesModule } from './resources/employee-roles/employee-roles.module';
import { EmployeesModule } from './resources/employees/employees.module';
import { BranchesModule } from './resources/branches/branches.module';
import { CustomersModule } from './resources/customers/customers.module';
import { InventoryModule } from './resources/inventory/inventory.module';
import { PaymentMethodsModule } from './resources/payment_methods/payment_methods.module';
import { PromotionsModule } from './resources/promotions/promotions.module';
import { FastAccessModule } from './resources/fast-access/fast-access.module';
import { CategoriesModule } from './resources/categories/categories.module';
import { GroupsModule } from './resources/groups/groups.module';
import { SalesModule } from './resources/sales/sales.module';
import { RefundsModule } from './resources/refunds/refunds.module';
import { DisplayContentModule } from './resources/display-content/display-content.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { MssqlModule } from './resources/mssql/mssql.module';
import { LoggerModule } from 'nestjs-pino';
import { DateModule } from './common/date/date.module';

import pino from 'pino';
import * as fs from 'fs';
import { MssqlModule2 } from './resources/mssql2/mssql2.module';
import { NtpModule } from './common/ntp/ntp.module';
import { VersionModule } from './resources/version/version.module';
import { VergiModule } from './resources/vergi/vergi.module';
import { BugsinkModule } from './common/bugsink/bugsink.module';
import { GoqueueModule } from './resources/goqueue/goqueue.module';
import { BirlikkartModule } from './resources/birlikkart/birlikkart.module';
import { GiderpusulasiModule } from './resources/giderpusulasi/giderpusulasi.module';
import { TahsilatModule } from './resources/tahsilat/tahsilat.module';
import { SatisModule } from './resources/satis/satis.module';
import { TediyeModule } from './resources/tediye/tediye.module';
@Module({
  imports: [
    SentryModule.forRoot(),
    VersionModule,
    NtpModule,
    DateModule,
    ConfigModule.forRoot({ isGlobal: true }),
    PrismaModule,
    AuthModule,
    RolesModule,
    PermissionsModule,
    EmployeeRolesModule,
    EmployeesModule,
    BranchesModule,
    CustomersModule,
    InventoryModule,
    PaymentMethodsModule,
    PromotionsModule,
    FastAccessModule,
    CategoriesModule,
    GroupsModule,
    SalesModule,
    RefundsModule,
    MssqlModule,
    MssqlModule2,
    DisplayContentModule,
    ServeStaticModule.forRoot({
      rootPath: join(process.cwd(), 'uploads'),
      serveRoot: '/uploads',
    }),
    LoggerModule.forRoot({
      pinoHttp: {
        // Loglar belirlenen dosyaya yazılır.
        stream: (() => {
          try {
            // Ensure logs directory exists
            if (!fs.existsSync('./logs')) {
              fs.mkdirSync('./logs', { recursive: true });
            }
            return pino.destination('./logs/app.log');
          } catch (error) {
            console.warn(
              'Could not create log file, falling back to stdout:',
              error.message,
            );
            return process.stdout;
          }
        })(),
        // İsterseniz diğer pino ayarlarını da buraya ekleyebilirsiniz.
      },
    }),
    VergiModule,
    BugsinkModule,
    GoqueueModule,
    BirlikkartModule,
    GiderpusulasiModule,
    TahsilatModule,
    SatisModule,
    TediyeModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
