package sqlite3
//go:cgo_import_dynamic getenv getenv#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic dlerror dlerror#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic localtime localtime#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic __libc_start_main __libc_start_main#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic __errno_location __errno_location#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic unlink unlink#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic strncmp strncmp#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic _ITM_deregisterTMCloneTable _ITM_deregisterTMCloneTable ""
//go:cgo_import_dynamic mkdir mkdir#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic qsort qsort#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic readlink readlink#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic write write#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic getpid getpid#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic lstat64 lstat64#GLIBC_2.33 "libc.so.6"
//go:cgo_import_dynamic rmdir rmdir#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic strlen strlen#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic __stack_chk_fail __stack_chk_fail#GLIBC_2.4 "libc.so.6"
//go:cgo_import_dynamic stat64 stat64#GLIBC_2.33 "libc.so.6"
//go:cgo_import_dynamic strchr strchr#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pthread_mutex_destroy pthread_mutex_destroy#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pthread_mutexattr_settype pthread_mutexattr_settype#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic nanosleep nanosleep#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic strrchr strrchr#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic gettimeofday gettimeofday#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic memset memset#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic geteuid geteuid#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic getcwd getcwd#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic close close#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic strspn strspn#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pthread_mutex_trylock pthread_mutex_trylock#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic strcspn strcspn#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic memchr memchr#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic read read#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic memcmp memcmp#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic utimes utimes#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic strcmp strcmp#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic dlopen dlopen#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic __memcpy_chk __memcpy_chk#GLIBC_2.3.4 "libc.so.6"
//go:cgo_import_dynamic __gmon_start__ __gmon_start__ ""
//go:cgo_import_dynamic memcpy memcpy#GLIBC_2.14 "libc.so.6"
//go:cgo_import_dynamic time time#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pwrite64 pwrite64#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic mmap64 mmap64#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pthread_mutex_unlock pthread_mutex_unlock#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic malloc malloc#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic mremap mremap#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic realloc realloc#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic munmap munmap#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic ftruncate64 ftruncate64#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic fchmod fchmod#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pthread_create pthread_create#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic open64 open64#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic fcntl64 fcntl64#GLIBC_2.28 "libc.so.6"
//go:cgo_import_dynamic memmove memmove#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic fsync fsync#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pread64 pread64#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic access access#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic fchown fchown#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic sysconf sysconf#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic dlsym dlsym#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic __memset_chk __memset_chk#GLIBC_2.3.4 "libc.so.6"
//go:cgo_import_dynamic fstat64 fstat64#GLIBC_2.33 "libc.so.6"
//go:cgo_import_dynamic _ITM_registerTMCloneTable _ITM_registerTMCloneTable ""
//go:cgo_import_dynamic pthread_mutexattr_init pthread_mutexattr_init#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic pthread_join pthread_join#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic dlclose dlclose#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic pthread_mutex_init pthread_mutex_init#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic pthread_mutexattr_destroy pthread_mutexattr_destroy#GLIBC_2.34 "libc.so.6"
//go:cgo_import_dynamic pthread_mutex_lock pthread_mutex_lock#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic __cxa_finalize __cxa_finalize#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic free free#GLIBC_2.2.5 "libc.so.6"
//go:cgo_import_dynamic _ _ "libc.so.6"
