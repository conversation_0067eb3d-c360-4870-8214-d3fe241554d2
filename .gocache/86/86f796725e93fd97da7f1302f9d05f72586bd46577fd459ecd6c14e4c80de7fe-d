// Code generated by cmd/cgo; DO NOT EDIT.

//line /home/<USER>/go/pkg/mod/github.com/mattn/go-sqlite3@v1.14.31/callback.go:1:1
// Copyright (C) 2019 <PERSON><PERSON><PERSON> <<EMAIL>>.
//
// Use of this source code is governed by an MIT-style
// license that can be found in the LICENSE file.

package sqlite3; import _cgo_unsafe "unsafe"

// You can't export a Go function to C and have definitions in the C
// preamble in the same file, so we have to have callbackTrampoline in
// its own file. Because we need a separate file anyway, the support
// code for SQLite custom functions is in here.

/*
#ifndef USE_LIBSQLITE3
#include "sqlite3-binding.h"
#else
#include <sqlite3.h>
#endif
#include <stdlib.h>

void _sqlite3_result_text(sqlite3_context* ctx, const char* s);
void _sqlite3_result_blob(sqlite3_context* ctx, const void* b, int l);
*/
import _ "unsafe"

import (
	"errors"
	"fmt"
	"math"
	"reflect"
	"sync"
	"unsafe"
)

//export callbackTrampoline
func callbackTrampoline(ctx * /*line :36:30*/_Ctype_sqlite3_context /*line :36:47*/, argc int, argv ** /*line :36:66*/_Ctype_sqlite3_value /*line :36:81*/) {
	args := (*[(math.MaxInt32 - 1) / unsafe.Sizeof((* /*line :37:51*/_Ctype_sqlite3_value /*line :37:66*/)(nil))]* /*line :37:75*/_Ctype_sqlite3_value /*line :37:90*/)(unsafe.Pointer(argv))[:argc:argc]
	fi := lookupHandle(func() _cgo_unsafe.Pointer{ _cgo0 := /*line :38:41*/ctx; _cgoCheckPointer(_cgo0, nil); return /*line :38:45*/_Cfunc_sqlite3_user_data(_cgo0); }()).(*functionInfo)
	fi.Call(ctx, args)
}

//export stepTrampoline
func stepTrampoline(ctx * /*line :43:26*/_Ctype_sqlite3_context /*line :43:43*/, argc  /*line :43:50*/_Ctype_int /*line :43:55*/, argv ** /*line :43:64*/_Ctype_sqlite3_value /*line :43:79*/) {
	args := (*[(math.MaxInt32 - 1) / unsafe.Sizeof((* /*line :44:51*/_Ctype_sqlite3_value /*line :44:66*/)(nil))]* /*line :44:75*/_Ctype_sqlite3_value /*line :44:90*/)(unsafe.Pointer(argv))[:int(argc):int(argc)]
	ai := lookupHandle(func() _cgo_unsafe.Pointer{ _cgo0 := /*line :45:41*/ctx; _cgoCheckPointer(_cgo0, nil); return /*line :45:45*/_Cfunc_sqlite3_user_data(_cgo0); }()).(*aggInfo)
	ai.Step(ctx, args)
}

//export doneTrampoline
func doneTrampoline(ctx * /*line :50:26*/_Ctype_sqlite3_context /*line :50:43*/) {
	ai := lookupHandle(func() _cgo_unsafe.Pointer{ _cgo0 := /*line :51:41*/ctx; _cgoCheckPointer(_cgo0, nil); return /*line :51:45*/_Cfunc_sqlite3_user_data(_cgo0); }()).(*aggInfo)
	ai.Done(ctx)
}

//export compareTrampoline
func compareTrampoline(handlePtr unsafe.Pointer, la  /*line :56:53*/_Ctype_int /*line :56:58*/, a * /*line :56:63*/_Ctype_char /*line :56:69*/, lb  /*line :56:74*/_Ctype_int /*line :56:79*/, b * /*line :56:84*/_Ctype_char /*line :56:90*/)  /*line :56:92*/_Ctype_int /*line :56:97*/ {
	cmp := lookupHandle(handlePtr).(func(string, string) int)
	return  /*line :58:9*/_Ctype_int /*line :58:14*/(cmp(( /*line :58:19*/_Cfunc_GoStringN /*line :58:29*/)(a, la), ( /*line :58:39*/_Cfunc_GoStringN /*line :58:49*/)(b, lb)))
}

//export commitHookTrampoline
func commitHookTrampoline(handle unsafe.Pointer) int {
	callback := lookupHandle(handle).(func() int)
	return callback()
}

//export rollbackHookTrampoline
func rollbackHookTrampoline(handle unsafe.Pointer) {
	callback := lookupHandle(handle).(func())
	callback()
}

//export updateHookTrampoline
func updateHookTrampoline(handle unsafe.Pointer, op int, db * /*line :74:62*/_Ctype_char /*line :74:68*/, table * /*line :74:77*/_Ctype_char /*line :74:83*/, rowid int64) {
	callback := lookupHandle(handle).(func(int, string, string, int64))
	callback(op, ( /*line :76:15*/_Cfunc_GoString /*line :76:24*/)(db), ( /*line :76:31*/_Cfunc_GoString /*line :76:40*/)(table), rowid)
}

//export authorizerTrampoline
func authorizerTrampoline(handle unsafe.Pointer, op int, arg1 * /*line :80:64*/_Ctype_char /*line :80:70*/, arg2 * /*line :80:78*/_Ctype_char /*line :80:84*/, arg3 * /*line :80:92*/_Ctype_char /*line :80:98*/) int {
	callback := lookupHandle(handle).(func(int, string, string, string) int)
	return callback(op, ( /*line :82:22*/_Cfunc_GoString /*line :82:31*/)(arg1), ( /*line :82:40*/_Cfunc_GoString /*line :82:49*/)(arg2), ( /*line :82:58*/_Cfunc_GoString /*line :82:67*/)(arg3))
}

//export preUpdateHookTrampoline
func preUpdateHookTrampoline(handle unsafe.Pointer, dbHandle uintptr, op int, db * /*line :86:83*/_Ctype_char /*line :86:89*/, table * /*line :86:98*/_Ctype_char /*line :86:104*/, oldrowid int64, newrowid int64) {
	hval := lookupHandleVal(handle)
	data := SQLitePreUpdateData{
		Conn:         hval.db,
		Op:           op,
		DatabaseName: ( /*line :91:17*/_Cfunc_GoString /*line :91:26*/)(db),
		TableName:    ( /*line :92:17*/_Cfunc_GoString /*line :92:26*/)(table),
		OldRowID:     oldrowid,
		NewRowID:     newrowid,
	}
	callback := hval.val.(func(SQLitePreUpdateData))
	callback(data)
}

// Use handles to avoid passing Go pointers to C.
type handleVal struct {
	db  *SQLiteConn
	val any
}

var handleLock sync.Mutex
var handleVals = make(map[unsafe.Pointer]handleVal)

func newHandle(db *SQLiteConn, v any) unsafe.Pointer {
	handleLock.Lock()
	defer handleLock.Unlock()
	val := handleVal{db: db, val: v}
	var p unsafe.Pointer = ( /*line :113:25*/_Cfunc__CMalloc /*line :113:32*/)( /*line :113:34*/_Ctype_size_t /*line :113:42*/(1))
	if p == nil {
		panic("can't allocate 'cgo-pointer hack index pointer': ptr == nil")
	}
	handleVals[p] = val
	return p
}

func lookupHandleVal(handle unsafe.Pointer) handleVal {
	handleLock.Lock()
	defer handleLock.Unlock()
	return handleVals[handle]
}

func lookupHandle(handle unsafe.Pointer) any {
	return lookupHandleVal(handle).val
}

func deleteHandles(db *SQLiteConn) {
	handleLock.Lock()
	defer handleLock.Unlock()
	for handle, val := range handleVals {
		if val.db == db {
			delete(handleVals, handle)
			func() { _cgo0 := /*line :137:11*/handle; _cgoCheckPointer(_cgo0, nil); /*line :137:18*/_Cfunc_free(_cgo0); }()
		}
	}
}

// This is only here so that tests can refer to it.
type callbackArgRaw  /*line :143:21*/_Ctype_sqlite3_value /*line :143:36*/

type callbackArgConverter func(* /*line :145:33*/_Ctype_sqlite3_value /*line :145:48*/) (reflect.Value, error)

type callbackArgCast struct {
	f   callbackArgConverter
	typ reflect.Type
}

func (c callbackArgCast) Run(v * /*line :152:33*/_Ctype_sqlite3_value /*line :152:48*/) (reflect.Value, error) {
	val, err := c.f(v)
	if err != nil {
		return reflect.Value{}, err
	}
	if !val.Type().ConvertibleTo(c.typ) {
		return reflect.Value{}, fmt.Errorf("cannot convert %s to %s", val.Type(), c.typ)
	}
	return val.Convert(c.typ), nil
}

func callbackArgInt64(v * /*line :163:26*/_Ctype_sqlite3_value /*line :163:41*/) (reflect.Value, error) {
	if func() _Ctype_int{ _cgo0 := /*line :164:26*/v; _cgoCheckPointer(_cgo0, nil); return /*line :164:28*/_Cfunc_sqlite3_value_type(_cgo0); }() != ( /*line :164:32*/_Ciconst_SQLITE_INTEGER /*line :164:47*/) {
		return reflect.Value{}, fmt.Errorf("argument must be an INTEGER")
	}
	return reflect.ValueOf(int64(func() _Ctype_sqlite3_int64{ _cgo0 := /*line :167:53*/v; _cgoCheckPointer(_cgo0, nil); return /*line :167:55*/_Cfunc_sqlite3_value_int64(_cgo0); }())), nil
}

func callbackArgBool(v * /*line :170:25*/_Ctype_sqlite3_value /*line :170:40*/) (reflect.Value, error) {
	if func() _Ctype_int{ _cgo0 := /*line :171:26*/v; _cgoCheckPointer(_cgo0, nil); return /*line :171:28*/_Cfunc_sqlite3_value_type(_cgo0); }() != ( /*line :171:32*/_Ciconst_SQLITE_INTEGER /*line :171:47*/) {
		return reflect.Value{}, fmt.Errorf("argument must be an INTEGER")
	}
	i := int64(func() _Ctype_sqlite3_int64{ _cgo0 := /*line :174:35*/v; _cgoCheckPointer(_cgo0, nil); return /*line :174:37*/_Cfunc_sqlite3_value_int64(_cgo0); }())
	val := false
	if i != 0 {
		val = true
	}
	return reflect.ValueOf(val), nil
}

func callbackArgFloat64(v * /*line :182:28*/_Ctype_sqlite3_value /*line :182:43*/) (reflect.Value, error) {
	if func() _Ctype_int{ _cgo0 := /*line :183:26*/v; _cgoCheckPointer(_cgo0, nil); return /*line :183:28*/_Cfunc_sqlite3_value_type(_cgo0); }() != ( /*line :183:32*/_Ciconst_SQLITE_FLOAT /*line :183:45*/) {
		return reflect.Value{}, fmt.Errorf("argument must be a FLOAT")
	}
	return reflect.ValueOf(float64(func() _Ctype_double{ _cgo0 := /*line :186:56*/v; _cgoCheckPointer(_cgo0, nil); return /*line :186:58*/_Cfunc_sqlite3_value_double(_cgo0); }())), nil
}

func callbackArgBytes(v * /*line :189:26*/_Ctype_sqlite3_value /*line :189:41*/) (reflect.Value, error) {
	switch func() _Ctype_int{ _cgo0 := /*line :190:30*/v; _cgoCheckPointer(_cgo0, nil); return /*line :190:32*/_Cfunc_sqlite3_value_type(_cgo0); }() {
	case ( /*line :191:7*/_Ciconst_SQLITE_BLOB /*line :191:19*/):
		l := func() _Ctype_int{ _cgo0 := /*line :192:30*/v; _cgoCheckPointer(_cgo0, nil); return /*line :192:32*/_Cfunc_sqlite3_value_bytes(_cgo0); }()
		p := func() _cgo_unsafe.Pointer{ _cgo0 := /*line :193:29*/v; _cgoCheckPointer(_cgo0, nil); return /*line :193:31*/_Cfunc_sqlite3_value_blob(_cgo0); }()
		return reflect.ValueOf(func() []byte{ _cgo0 := /*line :194:36*/p; var _cgo1 _Ctype_int = /*line :194:39*/l; _cgoCheckPointer(_cgo0, nil); return /*line :194:41*/_Cfunc_GoBytes(_cgo0, _cgo1); }()), nil
	case ( /*line :195:7*/_Ciconst_SQLITE_TEXT /*line :195:19*/):
		l := func() _Ctype_int{ _cgo0 := /*line :196:30*/v; _cgoCheckPointer(_cgo0, nil); return /*line :196:32*/_Cfunc_sqlite3_value_bytes(_cgo0); }()
		c := unsafe.Pointer(func() *_Ctype_uchar{ _cgo0 := /*line :197:44*/v; _cgoCheckPointer(_cgo0, nil); return /*line :197:46*/_Cfunc_sqlite3_value_text(_cgo0); }())
		return reflect.ValueOf(func() []byte{ _cgo0 := /*line :198:36*/c; var _cgo1 _Ctype_int = /*line :198:39*/l; _cgoCheckPointer(_cgo0, nil); return /*line :198:41*/_Cfunc_GoBytes(_cgo0, _cgo1); }()), nil
	default:
		return reflect.Value{}, fmt.Errorf("argument must be BLOB or TEXT")
	}
}

func callbackArgString(v * /*line :204:27*/_Ctype_sqlite3_value /*line :204:42*/) (reflect.Value, error) {
	switch func() _Ctype_int{ _cgo0 := /*line :205:30*/v; _cgoCheckPointer(_cgo0, nil); return /*line :205:32*/_Cfunc_sqlite3_value_type(_cgo0); }() {
	case ( /*line :206:7*/_Ciconst_SQLITE_BLOB /*line :206:19*/):
		l := func() _Ctype_int{ _cgo0 := /*line :207:30*/v; _cgoCheckPointer(_cgo0, nil); return /*line :207:32*/_Cfunc_sqlite3_value_bytes(_cgo0); }()
		p := (* /*line :208:10*/_Ctype_char /*line :208:16*/)(func() _cgo_unsafe.Pointer{ _cgo0 := /*line :208:39*/v; _cgoCheckPointer(_cgo0, nil); return /*line :208:41*/_Cfunc_sqlite3_value_blob(_cgo0); }())
		return reflect.ValueOf(( /*line :209:26*/_Cfunc_GoStringN /*line :209:36*/)(p, l)), nil
	case ( /*line :210:7*/_Ciconst_SQLITE_TEXT /*line :210:19*/):
		c := (* /*line :211:10*/_Ctype_char /*line :211:16*/)(unsafe.Pointer(func() *_Ctype_uchar{ _cgo0 := /*line :211:54*/v; _cgoCheckPointer(_cgo0, nil); return /*line :211:56*/_Cfunc_sqlite3_value_text(_cgo0); }()))
		return reflect.ValueOf(( /*line :212:26*/_Cfunc_GoString /*line :212:35*/)(c)), nil
	default:
		return reflect.Value{}, fmt.Errorf("argument must be BLOB or TEXT")
	}
}

func callbackArgGeneric(v * /*line :218:28*/_Ctype_sqlite3_value /*line :218:43*/) (reflect.Value, error) {
	switch func() _Ctype_int{ _cgo0 := /*line :219:30*/v; _cgoCheckPointer(_cgo0, nil); return /*line :219:32*/_Cfunc_sqlite3_value_type(_cgo0); }() {
	case ( /*line :220:7*/_Ciconst_SQLITE_INTEGER /*line :220:22*/):
		return callbackArgInt64(v)
	case ( /*line :222:7*/_Ciconst_SQLITE_FLOAT /*line :222:20*/):
		return callbackArgFloat64(v)
	case ( /*line :224:7*/_Ciconst_SQLITE_TEXT /*line :224:19*/):
		return callbackArgString(v)
	case ( /*line :226:7*/_Ciconst_SQLITE_BLOB /*line :226:19*/):
		return callbackArgBytes(v)
	case ( /*line :228:7*/_Ciconst_SQLITE_NULL /*line :228:19*/):
		// Interpret NULL as a nil byte slice.
		var ret []byte
		return reflect.ValueOf(ret), nil
	default:
		panic("unreachable")
	}
}

func callbackArg(typ reflect.Type) (callbackArgConverter, error) {
	switch typ.Kind() {
	case reflect.Interface:
		if typ.NumMethod() != 0 {
			return nil, errors.New("the only supported interface type is any")
		}
		return callbackArgGeneric, nil
	case reflect.Slice:
		if typ.Elem().Kind() != reflect.Uint8 {
			return nil, errors.New("the only supported slice type is []byte")
		}
		return callbackArgBytes, nil
	case reflect.String:
		return callbackArgString, nil
	case reflect.Bool:
		return callbackArgBool, nil
	case reflect.Int64:
		return callbackArgInt64, nil
	case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Int, reflect.Uint:
		c := callbackArgCast{callbackArgInt64, typ}
		return c.Run, nil
	case reflect.Float64:
		return callbackArgFloat64, nil
	case reflect.Float32:
		c := callbackArgCast{callbackArgFloat64, typ}
		return c.Run, nil
	default:
		return nil, fmt.Errorf("don't know how to convert to %s", typ)
	}
}

func callbackConvertArgs(argv []* /*line :268:34*/_Ctype_sqlite3_value /*line :268:49*/, converters []callbackArgConverter, variadic callbackArgConverter) ([]reflect.Value, error) {
	var args []reflect.Value

	if len(argv) < len(converters) {
		return nil, fmt.Errorf("function requires at least %d arguments", len(converters))
	}

	for i, arg := range argv[:len(converters)] {
		v, err := converters[i](arg)
		if err != nil {
			return nil, err
		}
		args = append(args, v)
	}

	if variadic != nil {
		for _, arg := range argv[len(converters):] {
			v, err := variadic(arg)
			if err != nil {
				return nil, err
			}
			args = append(args, v)
		}
	}
	return args, nil
}

type callbackRetConverter func(* /*line :295:33*/_Ctype_sqlite3_context /*line :295:50*/, reflect.Value) error

func callbackRetInteger(ctx * /*line :297:30*/_Ctype_sqlite3_context /*line :297:47*/, v reflect.Value) error {
	switch v.Type().Kind() {
	case reflect.Int64:
	case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Int, reflect.Uint:
		v = v.Convert(reflect.TypeOf(int64(0)))
	case reflect.Bool:
		b := v.Interface().(bool)
		if b {
			v = reflect.ValueOf(int64(1))
		} else {
			v = reflect.ValueOf(int64(0))
		}
	default:
		return fmt.Errorf("cannot convert %s to INTEGER", v.Type())
	}

	func() { _cgo0 := /*line :313:25*/ctx; var _cgo1 _Ctype_sqlite3_int64 = _Ctype_sqlite3_int64 /*line :313:45*/(v.Interface().(int64)); _cgoCheckPointer(_cgo0, nil); /*line :313:69*/_Cfunc_sqlite3_result_int64(_cgo0, _cgo1); }()
	return nil
}

func callbackRetFloat(ctx * /*line :317:28*/_Ctype_sqlite3_context /*line :317:45*/, v reflect.Value) error {
	switch v.Type().Kind() {
	case reflect.Float64:
	case reflect.Float32:
		v = v.Convert(reflect.TypeOf(float64(0)))
	default:
		return fmt.Errorf("cannot convert %s to FLOAT", v.Type())
	}

	func() { _cgo0 := /*line :326:26*/ctx; var _cgo1 _Ctype_double = _Ctype_double /*line :326:39*/(v.Interface().(float64)); _cgoCheckPointer(_cgo0, nil); /*line :326:65*/_Cfunc_sqlite3_result_double(_cgo0, _cgo1); }()
	return nil
}

func callbackRetBlob(ctx * /*line :330:27*/_Ctype_sqlite3_context /*line :330:44*/, v reflect.Value) error {
	if v.Type().Kind() != reflect.Slice || v.Type().Elem().Kind() != reflect.Uint8 {
		return fmt.Errorf("cannot convert %s to BLOB", v.Type())
	}
	i := v.Interface()
	if i == nil || len(i.([]byte)) == 0 {
		func() { _cgo0 := /*line :336:25*/ctx; _cgoCheckPointer(_cgo0, nil); /*line :336:29*/_Cfunc_sqlite3_result_null(_cgo0); }()
	} else {
		bs := i.([]byte)
		func() { _cgo0 := /*line :339:26*/ctx; _cgoIndex1 := &/*line :339:47*/bs; _cgo1 := /*line :339:31*/unsafe.Pointer(&(*_cgoIndex1)[0]); var _cgo2 _Ctype_int = _Ctype_int /*line :339:60*/(len(bs)); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo1, *_cgoIndex1); /*line :339:70*/_Cfunc__sqlite3_result_blob(_cgo0, _cgo1, _cgo2); }()
	}
	return nil
}

func callbackRetText(ctx * /*line :344:27*/_Ctype_sqlite3_context /*line :344:44*/, v reflect.Value) error {
	if v.Type().Kind() != reflect.String {
		return fmt.Errorf("cannot convert %s to TEXT", v.Type())
	}
	cstr := ( /*line :348:10*/_Cfunc_CString /*line :348:18*/)(v.Interface().(string))
	func() { _cgo0 := /*line :349:25*/ctx; var _cgo1 *_Ctype_char = /*line :349:30*/cstr; _cgoCheckPointer(_cgo0, nil); /*line :349:35*/_Cfunc__sqlite3_result_text(_cgo0, _cgo1); }()
	return nil
}

func callbackRetNil(ctx * /*line :353:26*/_Ctype_sqlite3_context /*line :353:43*/, v reflect.Value) error {
	return nil
}

func callbackRetGeneric(ctx * /*line :357:30*/_Ctype_sqlite3_context /*line :357:47*/, v reflect.Value) error {
	if v.IsNil() {
		func() { _cgo0 := /*line :359:25*/ctx; _cgoCheckPointer(_cgo0, nil); /*line :359:29*/_Cfunc_sqlite3_result_null(_cgo0); }()
		return nil
	}

	cb, err := callbackRet(v.Elem().Type())
	if err != nil {
		return err
	}

	return cb(ctx, v.Elem())
}

func callbackRet(typ reflect.Type) (callbackRetConverter, error) {
	switch typ.Kind() {
	case reflect.Interface:
		errorInterface := reflect.TypeOf((*error)(nil)).Elem()
		if typ.Implements(errorInterface) {
			return callbackRetNil, nil
		}

		if typ.NumMethod() == 0 {
			return callbackRetGeneric, nil
		}

		fallthrough
	case reflect.Slice:
		if typ.Elem().Kind() != reflect.Uint8 {
			return nil, errors.New("the only supported slice type is []byte")
		}
		return callbackRetBlob, nil
	case reflect.String:
		return callbackRetText, nil
	case reflect.Bool, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Int, reflect.Uint:
		return callbackRetInteger, nil
	case reflect.Float32, reflect.Float64:
		return callbackRetFloat, nil
	default:
		return nil, fmt.Errorf("don't know how to convert to %s", typ)
	}
}

func callbackError(ctx * /*line :400:25*/_Ctype_sqlite3_context /*line :400:42*/, err error) {
	cstr := ( /*line :401:10*/_Cfunc_CString /*line :401:18*/)(err.Error())
	defer func() func() { _cgo0 := /*line :402:15*/unsafe.Pointer(cstr); return func() { _cgoCheckPointer(_cgo0, nil); /*line :402:36*/_Cfunc_free(_cgo0); }}()()
	func() { _cgo0 := /*line :403:25*/ctx; var _cgo1 *_Ctype_char = /*line :403:30*/cstr; var _cgo2 _Ctype_int = _Ctype_int /*line :403:41*/(-1); _cgoCheckPointer(_cgo0, nil); /*line :403:46*/_Cfunc_sqlite3_result_error(_cgo0, _cgo1, _cgo2); }()
}

// Test support code. Tests are not allowed to import "C", so we can't
// declare any functions that use C.sqlite3_value.
func callbackSyntheticForTests(v reflect.Value, err error) callbackArgConverter {
	return func(* /*line :409:15*/_Ctype_sqlite3_value /*line :409:30*/) (reflect.Value, error) {
		return v, err
	}
}
