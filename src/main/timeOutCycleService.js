import { Client } from 'pg'
import dotenv from 'dotenv'
import { getDatabase } from './database.js'

dotenv.config()
// Console logging is enabled for diagnostics; use env to control verbosity if needed

class TimeOutCycleService {
  constructor() {
    this.intervalId = null
    this.isRunning = false
    this.timeCycle = parseInt(process.env.TIME_OUT_CYCLE) || parseInt(process.env.TIME_CYCLE) || 5

    this.pgConfig = {
      user: process.env.POSTGRES_USER,
      host: process.env.POSTGRES_HOST,
      database: process.env.POSTGRES_DB,
      password: process.env.POSTGRES_PASSWORD,
      port: process.env.POSTGRES_PORT,
      ssl: process.env.POSTGRES_SSL === 'true',
    }

    // console.log removed for production
    this.pgHasCekNo = null
  }

  async connectToPostgreSQL() {
    const client = new Client(this.pgConfig)

    try {
      console.log('[TimeOutCycle][PG] Connecting with config:', {
        host: this.pgConfig.host,
        database: this.pgConfig.database,
        user: this.pgConfig.user,
        port: this.pgConfig.port,
        ssl: this.pgConfig.ssl,
      })
      await client.connect()
      console.log('[TimeOutCycle][PG] ✅ Connection established')
      return client
    } catch (error) {
      console.error('[TimeOutCycle][PG] ❌ Connection failed:', {
        message: error.message,
        code: error.code,
        detail: error.detail,
        hint: error.hint,
        stack: error.stack,
      })
      throw error
    }
  }

  async testConnection() {
    let client = null

    try {
      client = await this.connectToPostgreSQL()

      // Basit bir test sorgusu
      console.log('[TimeOutCycle][PG] Running test query: SELECT 1 as test')
      const result = await client.query('SELECT 1 as test')

      if (result.rows.length > 0 && result.rows[0].test === 1) {
        console.log('[TimeOutCycle][PG] ✅ Test query successful')
        return true
      } else {
        console.error('[TimeOutCycle][PG] ❌ Test failed: Invalid response', result)
        return false
      }
    } catch (error) {
      console.error('[TimeOutCycle][PG] ❌ Test failed:', {
        message: error.message,
        code: error.code,
        detail: error.detail,
        hint: error.hint,
        stack: error.stack,
      })
      return false
    } finally {
      if (client) {
        await client.end()
      }
    }
  }

  async queryInventory() {
    let client = null

    try {
      client = await this.connectToPostgreSQL()

      const query = 'SELECT * FROM inventory ORDER BY id LIMIT 1'
      const result = await client.query(query)

      if (result.rows.length > 0) {
        //   console.log('Inventory query result:', result.rows[0])
        return result.rows[0]
      } else {
        // console.log removed for production
        return null
      }
    } catch (error) {
      console.error('Inventory query error:', error.message)
      return null
    } finally {
      if (client) {
        await client.end()
      }
    }
  }

  async executeTimeCycle() {
    try {
      console.log(`[TimeOutCycle] ▶︎ Executing cycle at ${new Date().toLocaleString('tr-TR')}`)

      // Önce bağlantı kontrolü yap
      const connectionTest = await this.testConnection()

      if (!connectionTest) {
        // console.log removed for production
        return
      }
      /*
      // Bağlantı varsa inventory sorgusunu çalıştır
      const inventoryRecord = await this.queryInventory()

      if (inventoryRecord) {
        // log removed
      } else {
        // log removed
      }
*/
      // Pending sales işlemlerini yap
      try {
        const salesResult = await this.processPendingSales()
        console.log(
          `[TimeOutCycle] Sales processed: total=${salesResult.processed}, success=${salesResult.success}, failed=${salesResult.failed}`
        )
      } catch (salesError) {
        console.error('Error processing pending sales:', salesError.message)
      }

      // Pending refunds işlemlerini yap
      try {
        const refundsResult = await this.processPendingRefunds()
        console.log(
          `[TimeOutCycle] Refunds processed: total=${refundsResult.processed}, success=${refundsResult.success}, failed=${refundsResult.failed}`
        )
      } catch (refundsError) {
        console.error('Error processing pending refunds:', refundsError.message)
      }
    } catch (error) {
      console.error('Time cycle execution error:', error.message)
    }
  }

  async start() {
    console.log(`[TimeOutCycle] Starting service (cycle=${this.timeCycle}s) ...`)
    if (this.isRunning) {
      return
    }

    // console.log removed for production

    // Başlangıçta bağlantı kontrolü yap
    // console.log removed for production
    const initialConnectionTest = await this.testConnection()

    if (!initialConnectionTest) {
      // console.warn removed for production
    }

    // İlk sorguyu hemen çalıştır
    this.executeTimeCycle()

    // Sonrasında belirtilen aralıklarla çalıştır
    this.intervalId = setInterval(() => {
      this.executeTimeCycle()
    }, this.timeCycle * 1000)

    this.isRunning = true
  }

  stop() {
    if (!this.isRunning) {
      return
    }

    // console.log removed for production

    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    this.isRunning = false
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      timeCycle: this.timeCycle,
      pgConfig: {
        ...this.pgConfig,
        password: '***', // Şifreyi gizle
      },
    }
  }

  updateTimeCycle(newTimeCycle) {
    if (newTimeCycle && newTimeCycle > 0) {
      this.timeCycle = newTimeCycle

      if (this.isRunning) {
        this.stop()
        this.start()
      }

      // console.log removed for production
    }
  }

  /* --------------------------------- CORE ----------------------------------- */

  async getSaleDetails(saleId) {
    const db = getDatabase()

    const saleStmt = db.prepare(`SELECT * FROM sales WHERE id = ?`)
    const sale = saleStmt.get(saleId)

    if (!sale) {
      throw new Error(`Sale not found: ${saleId}`)
    }

    // Eğer initiated_by null ise varsayılan bir çalışan ata
    if (!sale.initiated_by) {
      const defaultEmployeeStmt = db.prepare(`
        SELECT uuid FROM employees WHERE deleted_at IS NULL LIMIT 1
      `)
      const defaultEmployee = defaultEmployeeStmt.get()

      if (defaultEmployee) {
        const updateSaleStmt = db.prepare(`
          UPDATE sales SET initiated_by = ? WHERE id = ?
        `)
        updateSaleStmt.run(defaultEmployee.uuid, saleId)
        sale.initiated_by = defaultEmployee.uuid
        // log removed
      } else {
        throw new Error(`No employees available to assign to sale ${saleId}`)
      }
    }

    // Direkt items ve payments al (stabilizasyon beklemeden)
    const items = db.prepare(`SELECT * FROM sale_items WHERE sale_uuid = ?`).all(sale.uuid)
    const payments = db.prepare(`SELECT * FROM payments WHERE sale_uuid = ?`).all(sale.uuid)

    console.log('[TimeOutCycle][SQLite] Loaded sale details:', {
      saleId,
      saleUuid: sale.uuid,
      itemsCount: items.length,
      paymentsCount: payments.length,
    })

    return {
      ...sale,
      items,
      payments,
    }
  }

  async insertSaleToPostgreSQL(saleData) {
    let client = null

    try {
      client = await this.connectToPostgreSQL()

      // Items kontrolü - eğer items yoksa insert yapma
      if (!saleData.items || saleData.items.length === 0) {
        console.error(
          `[TimeCycle] [Sales] ❌ Cannot insert sale: No items found for sale ${saleData.uuid}`
        )
        return false
      }

      // Items detaylarını logla
      // verbose sample items logs removed

      // Her item'ın gerekli alanlarının olduğunu kontrol et
      const invalidItems = saleData.items.filter(
        item => !item.inventory_code || item.quantity == null || item.unit_price == null
      )

      if (invalidItems.length > 0) {
        console.error(
          `[TimeCycle] [Sales] ❌ Sale ${saleData.uuid} has ${invalidItems.length} invalid items (missing inventory_code, quantity, or unit_price)`
        )
        console.error(`[TimeCycle] [Sales] Invalid items:`, invalidItems)
        return false
      }

      // PostgreSQL'e insert işlemi başla
      console.log('[TimeOutCycle][PG] BEGIN transaction for sale', saleData.uuid)
      //  console.log('[TimeOutCycle][PG] BEGIN transaction for refund', refundData.uuid)
      await client.query('BEGIN')

      // Compute remaining and exceeded amounts from payments
      const paidAmount = Array.isArray(saleData.payments)
        ? saleData.payments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0)
        : 0
      const totalPrice = parseFloat(saleData.total_price) || 0
      const remainingAmount = Math.max(totalPrice - paidAmount, 0)
      const exceededAmount = Math.max(paidAmount - totalPrice, 0)
      console.log('[TimeOutCycle][PG] Computed amounts:', {
        totalPrice,
        paidAmount,
        remainingAmount,
        exceededAmount,
      })

      // Fetch market info for market_id/subeip/subeno/kasano fallbacks if missing on sale
      let marketInfo = null
      try {
        const db = getDatabase()
        marketInfo = db.prepare('SELECT * FROM market_id WHERE id = 1').get() || null
        console.log('[TimeOutCycle][SQLite] market_id info:', marketInfo)
      } catch (e) {
        console.warn('[TimeOutCycle][SQLite] Could not read market_id:', e?.message)
        marketInfo = null
      }

      // received_sales tablosuna insert (hedef şema kullanıcı tarafından verildi)
      const salesInsertQuery = `
        INSERT INTO received_sales (
          uuid, status, initiated_by, workstation_uuid, promotion_id, receipt_number,
          customer, discount, discount_type, original_price, total_price,
          remaining_amount, exceeded_amount,
          subeip, subeno, kasano, market_id,
          cancel_reason, deleted_at, received_at, transmitted_at,
          created_at, updated_at
        ) VALUES (
          $1,$2,$3,$4,$5,$6,
          $7,$8,$9,$10,$11,
          $12,$13,
          $14,$15,$16,$17,
          $18,$19,$20,$21,
          $22,$23
        )
        ON CONFLICT (uuid) DO NOTHING
        RETURNING id
      `

      const salesParams = [
        saleData.uuid,
        saleData.status ?? 4,
        saleData.initiated_by,
        saleData.workstation_id || saleData.workstation_uuid || null,
        saleData.promotion_id || null,
        saleData.receipt_number,
        saleData.customer || null,
        saleData.discount || 0,
        saleData.discount_type || 1,
        saleData.original_price ?? saleData.originalPrice ?? totalPrice,
        totalPrice,
        remainingAmount,
        exceededAmount,
        saleData.subeip || marketInfo?.subeip || null,
        saleData.subeno || marketInfo?.subeno || null,
        saleData.kasano || marketInfo?.kasano || null,
        saleData.market_id || marketInfo?.market_id || null,
        saleData.cancel_reason || null,
        saleData.deleted_at || null,
        saleData.received_at || saleData.created_at || new Date().toISOString(),
        saleData.transmitted_at || null,
        saleData.created_at,
        saleData.updated_at,
      ]
      console.log('[TimeOutCycle][PG] Inserting into received_sales with params:', salesParams)
      let salesResult
      try {
        salesResult = await client.query(salesInsertQuery, salesParams)
      } catch (err) {
        console.error('[TimeOutCycle][PG] ❌ received_sales insert failed:', {
          message: err.message,
          code: err.code,
          detail: err.detail,
          hint: err.hint,
          position: err.position,
          schema: err.schema,
          table: err.table,
          column: err.column,
          stack: err.stack,
        })
        await client.query('ROLLBACK')
        return false
      }

      if (salesResult.rows.length === 0) {
        // log removed
        await client.query('ROLLBACK')
        return true
      }
      const postgresqlSaleId = saleData.items[0].sale_uuid

      //      console.log('sale Datas', saleData.items)
      // Sale items insert
      for (const item of saleData.items) {
        const invName = (item.inventory_name || item.name || item.productName || 'Ürün').toString()
        const itemInsertQuery = `
          INSERT INTO sale_items (
            sale_uuid, inventory_code,inventory_name, quantity, unit_price,
            total_price, tax_percent, created_at,perakende_vergi_kodu,perakende_vergi_orani,kdv_matrah
          ) VALUES ($1, $2, $3, $4, $5, $6, $7,$8,$9,$10,$11)
        `

        const itemParams = [
          item.sale_uuid,
          item.inventory_code,
          invName,
          item.quantity,
          item.unit_price,
          item.total_price,
          item.tax_percent,
          item.created_at,
          item.perakende_vergi_kodu,
          item.perakende_vergi_orani,
          item.kdv_matrah,
        ]
        console.log('[TimeOutCycle][PG] Inserting sale item with params:', itemParams)
        try {
          await client.query(itemInsertQuery, itemParams)
        } catch (err) {
          console.error('[TimeOutCycle][PG] ❌ sale_items insert failed:', {
            message: err.message,
            code: err.code,
            detail: err.detail,
            hint: err.hint,
            position: err.position,
            stack: err.stack,
            item,
          })
          await client.query('ROLLBACK')
          return false
        }
      }

      // Payments insert (optionally include cek_no if column exists)
      if (this.pgHasCekNo === null) {
        try {
          const colCheck = await client.query(
            `SELECT 1 FROM information_schema.columns WHERE table_name = 'payments' AND column_name = 'cek_no' LIMIT 1`
          )
          this.pgHasCekNo = (colCheck.rows?.length || 0) > 0
          console.log('[TimeOutCycle][PG] payments.cek_no exists:', this.pgHasCekNo)
        } catch (_) {
          this.pgHasCekNo = false
          console.log('[TimeOutCycle][PG] payments.cek_no check failed, assuming false')
        }
      }

      for (const payment of saleData.payments) {
        if (this.pgHasCekNo) {
          const q = `INSERT INTO payments (sale_uuid, payment_method, cek_no, amount, created_at) VALUES ($1,$2,$3,$4,$5)`
          const params = [
            postgresqlSaleId,
            payment.payment_method,
            payment.cek_no || null,
            payment.amount,
            payment.created_at,
          ]
          console.log('[TimeOutCycle][PG] Inserting payment with params:', params)
          try {
            await client.query(q, params)
          } catch (err) {
            console.error('[TimeOutCycle][PG] ❌ payments insert failed:', {
              message: err.message,
              code: err.code,
              detail: err.detail,
              hint: err.hint,
              position: err.position,
              stack: err.stack,
              payment,
            })
            await client.query('ROLLBACK')
            return false
          }
        } else {
          const q = `INSERT INTO payments (sale_uuid, payment_method, amount, created_at) VALUES ($1,$2,$3,$4)`
          const params = [
            postgresqlSaleId,
            payment.payment_method,
            payment.amount,
            payment.created_at,
          ]
          console.log('[TimeOutCycle][PG] Inserting payment with params:', params)
          try {
            await client.query(q, params)
          } catch (err) {
            console.error('[TimeOutCycle][PG] ❌ payments insert failed:', {
              message: err.message,
              code: err.code,
              detail: err.detail,
              hint: err.hint,
              position: err.position,
              stack: err.stack,
              payment,
            })
            await client.query('ROLLBACK')
            return false
          }
        }
      }

      await client.query('COMMIT')
      console.log('[TimeOutCycle][PG] ✅ COMMIT transaction for sale', saleData.uuid)
      return true
    } catch (error) {
      if (client) {
        console.error('[TimeOutCycle][PG] ❌ Error, ROLLBACK transaction for sale', saleData.uuid)
        await client.query('ROLLBACK')
      }
      console.error(`[TimeCycle] [Sales] ❌ Error inserting sale to PostgreSQL:`, {
        message: error.message,
        code: error.code,
        detail: error.detail,
        hint: error.hint,
        position: error.position,
        stack: error.stack,
      })
      return false
    } finally {
      if (client) {
        await client.end()
      }
    }
  }

  updateSaleTransmissionStatus(saleId) {
    const db = getDatabase()

    // Check if transferred column exists, if not add it
    try {
      db.prepare('SELECT transferred FROM sales LIMIT 0').all()
    } catch (error) {
      try {
        console.log('[TimeOutCycle][SQLite] Adding transferred column to sales')
        db.exec('ALTER TABLE sales ADD COLUMN transferred BOOLEAN DEFAULT false')
      } catch (alterError) {
        console.warn('[TimeCycle] [Sales] Could not add transferred column:', alterError.message)
      }
    }

    const stmt = db.prepare(`
      UPDATE sales SET transferred = true WHERE id = ?
    `)
    const info = stmt.run(saleId)
    console.log('[TimeOutCycle][SQLite] Marked sale as transferred:', {
      saleId,
      changes: info.changes,
    })
  }

  async processPendingSales() {
    const db = getDatabase()

    // Check if transferred column exists, if not add it
    try {
      db.prepare('SELECT transferred FROM sales LIMIT 0').all()
    } catch (error) {
      try {
        db.exec('ALTER TABLE sales ADD COLUMN transferred BOOLEAN DEFAULT false')
        // log removed
      } catch (alterError) {
        console.warn('[TimeCycle] [Sales] Could not add transferred column')
        return { processed: 0, success: 0, failed: 0 }
      }
    }

    // Only use transferred column
    const stmt = db.prepare(`
      SELECT id FROM sales
      WHERE status = 4 AND (transferred = false OR transferred IS NULL)
      ORDER BY created_at ASC
      LIMIT 10
    `)

    const pendingSales = stmt.all()
    console.log(
      '[TimeOutCycle][SQLite] Pending sales:',
      pendingSales.map(s => s.id)
    )

    let successCount = 0
    let failureCount = 0

    for (const sale of pendingSales) {
      try {
        const saleData = await this.getSaleDetails(sale.id)
        console.log('[TimeOutCycle] Processing sale:', {
          id: sale.id,
          uuid: saleData.uuid,
          items: saleData.items?.length || 0,
          payments: saleData.payments?.length || 0,
        })
        const insertSuccess = await this.insertSaleToPostgreSQL(saleData)

        if (insertSuccess) {
          this.updateSaleTransmissionStatus(sale.id)
          successCount++
          // log removed
        } else {
          failureCount++
          // log removed
        }
      } catch (error) {
        failureCount++
        // log removed
      }
    }

    return { processed: pendingSales.length, success: successCount, failed: failureCount }
  }

  /* --------------------------------- REFUNDS ----------------------------------- */

  async getRefundDetails(refundId) {
    const db = getDatabase()

    try {
      // Check if uuid column exists in the table
      const refundQuery = `SELECT * FROM sale_refunds WHERE id = ?`
      let hasUuidColumn = true

      try {
        db.prepare('SELECT uuid FROM sale_refunds LIMIT 0').all()
      } catch (error) {
        if (error.code === 'SQLITE_ERROR' && error.message.includes('no such column: uuid')) {
          hasUuidColumn = false
          // log removed
          try {
            db.exec('ALTER TABLE sale_refunds ADD COLUMN uuid TEXT')
            hasUuidColumn = true
          } catch (alterError) {
            console.error('[TimeCycle] [Refunds] Failed to add uuid column:', alterError)
          }
        }
      }

      const refundStmt = db.prepare(refundQuery)
      const refund = refundStmt.get(refundId)

      if (!refund) {
        throw new Error(`Refund not found: ${refundId}`)
      }

      // Ensure UUID exists if column is available
      if (hasUuidColumn && !refund.uuid) {
        const uuid = `REF-${Date.now()}-${refundId}-${Math.random().toString(36).substr(2, 6)}`
        const updateStmt = db.prepare('UPDATE sale_refunds SET uuid = ? WHERE id = ?')
        updateStmt.run(uuid, refundId)
        refund.uuid = uuid
        // log removed
      } else if (!hasUuidColumn) {
        refund.uuid = `REF-${Date.now()}-${refundId}-${Math.random().toString(36).substr(2, 6)}`
        // log removed
      }

      const itemsStmt = db.prepare(`SELECT * FROM sale_refund_items WHERE refundId = ?`)
      const items = itemsStmt.all(refundId)

      // Get default workstation if not present in refund
      let workstationUuid = refund.workstation_uuid
      if (!workstationUuid) {
        const workstationStmt = db.prepare('SELECT uuid FROM workstation_id LIMIT 1')
        const workstation = workstationStmt.get()
        workstationUuid = workstation?.uuid || 'ws-default'
      }

      return {
        ...refund,
        items,
        total_refund_amount: refund.totalAmount || refund.total_refund_amount,
        receipt_number: refund.refundNo || refund.receipt_number,
        initiated_by: refund.employeeId || refund.initiated_by,
        workstation_uuid: workstationUuid,
      }
    } catch (error) {
      console.error(`[TimeCycle] [Refunds] Error getting refund details for ${refundId}:`, error)
      throw new Error(`Failed to get refund details: ${error.message}`)
    }
  }

  async insertRefundToPostgreSQL(refundData) {
    let client = null

    try {
      client = await this.connectToPostgreSQL()

      if (!refundData.items || refundData.items.length === 0) {
        console.error(
          `[TimeCycle] [Refunds] ❌ Cannot insert refund: No items found for refund ${refundData.uuid}`
        )
        return false
      }

      await client.query('BEGIN')

      // Compute refund totals and defaults
      const totalRefund = parseFloat(refundData.total_refund_amount ?? refundData.total_price ?? 0)

      // Try to fetch market info (fallbacks for location fields)
      let marketInfo = null
      try {
        const db = getDatabase()
        marketInfo = db.prepare('SELECT * FROM market_id WHERE id = 1').get() || null
      } catch (_) {
        marketInfo = null
      }

      // Insert refund header into received_sales with full schema to satisfy NOT NULLs
      const refundInsertQuery = `
        INSERT INTO sale_refunds (
         sale_uuid, uuid, receipt_number, total_refund_amount, initiated_by, workstation_uuid
        ) VALUES (
          $1,$2,$3,$4,$5,$6
        )
        ON CONFLICT (uuid) DO NOTHING
        RETURNING id
      `

      const refundParams = [
        refundData.uuid,
        refundData.uuid,
        refundData.receipt_number,
        totalRefund, // original_price (required NOT NULL)
        refundData.initiated_by,
        refundData.workstation_uuid,
      ]
      console.log('[TimeOutCycle][PG] Inserting refund with params:', refundParams)
      let refundResult
      try {
        refundResult = await client.query(refundInsertQuery, refundParams)
      } catch (err) {
        console.error('[TimeOutCycle][PG] ❌ refund insert failed:', {
          message: err.message,
          code: err.code,
          detail: err.detail,
          hint: err.hint,
          position: err.position,
          stack: err.stack,
        })
        await client.query('ROLLBACK')
        return false
      }

      if (refundResult.rows.length === 0) {
        // log removed
        await client.query('ROLLBACK')
        return true
      }

      const postgresqlRefundId = refundResult.rows[0].id

      // Refund items insert
      for (const item of refundData.items) {
        const itemInsertQuery = `
          INSERT INTO sale_refund_items (
            refund_uuid, inventory_code,inventory_name, quantity, unit_price,
            total_price, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6,$7)
        `

        const unitPrice = item.unit_price ?? item.unitPrice ?? 0
        const totalPrice = item.total_price ?? item.totalPrice ?? unitPrice * (item.quantity || 0)
        const createdAt = item.created_at || refundData.created_at || new Date().toISOString()

        const params = [
          refundData.uuid,
          item.inventory_code,
          item.inventory_name,
          item.quantity,
          unitPrice,
          totalPrice,
          createdAt,
        ]
        console.log('[TimeOutCycle][PG] Inserting refund item with params:', params)
        try {
          await client.query(itemInsertQuery, params)
        } catch (err) {
          console.error('[TimeOutCycle][PG] ❌ refund_items insert failed:', {
            message: err.message,
            code: err.code,
            detail: err.detail,
            hint: err.hint,
            position: err.position,
            stack: err.stack,
            item,
          })
          await client.query('ROLLBACK')
          return false
        }
      }

      await client.query('COMMIT')
      console.log('[TimeOutCycle][PG] ✅ COMMIT transaction for refund', refundData.uuid)
      return true
    } catch (error) {
      if (client) {
        console.error(
          '[TimeOutCycle][PG] ❌ Error, ROLLBACK transaction for refund',
          refundData.uuid
        )
        await client.query('ROLLBACK')
      }
      console.error(`[TimeCycle] [Refunds] ❌ Error inserting refund to PostgreSQL:`, {
        message: error.message,
        code: error.code,
        detail: error.detail,
        hint: error.hint,
        position: error.position,
        stack: error.stack,
      })
      return false
    } finally {
      if (client) {
        await client.end()
      }
    }
  }

  updateRefundTransmissionStatus(refundId) {
    const db = getDatabase()

    // Check if transferred column exists, if not add it
    try {
      db.prepare('SELECT transferred FROM sale_refunds LIMIT 0').all()
    } catch (error) {
      try {
        db.exec('ALTER TABLE sale_refunds ADD COLUMN transferred BOOLEAN DEFAULT false')
        // log removed
      } catch (alterError) {
        console.warn('[TimeCycle] [Refunds] Could not add transferred column:', alterError.message)
        return
      }
    }

    const stmt = db.prepare(`
      UPDATE sale_refunds SET transferred = true WHERE id = ?
    `)
    stmt.run(refundId)
    // log removed
  }

  async processPendingRefunds() {
    const db = getDatabase()

    // Check if transferred column exists, if not add it
    try {
      db.prepare('SELECT transferred FROM sale_refunds LIMIT 0').all()
    } catch (error) {
      try {
        db.exec('ALTER TABLE sale_refunds ADD COLUMN transferred BOOLEAN DEFAULT false')
        // log removed
      } catch (alterError) {
        console.warn('[TimeCycle] [Refunds] Could not add transferred column, skipping refund sync')
        return { processed: 0, success: 0, failed: 0 }
      }
    }

    // Only use transferred column
    const stmt = db.prepare(`
      SELECT id FROM sale_refunds
      WHERE (transferred = false OR transferred IS NULL)
      ORDER BY created_at ASC
      LIMIT 10
    `)

    const pendingRefunds = stmt.all()
    // log removed
    if (pendingRefunds.length === 0) {
      return
    }
    let successCount = 0
    let failureCount = 0

    for (const refund of pendingRefunds) {
      try {
        const refundData = await this.getRefundDetails(refund.id)
        const insertSuccess = await this.insertRefundToPostgreSQL(refundData)

        if (insertSuccess) {
          this.updateRefundTransmissionStatus(refund.id)
          successCount++
          // log removed
        } else {
          failureCount++
          // log removed
        }
      } catch (error) {
        failureCount++
        // log removed
      }
    }

    return { processed: pendingRefunds.length, success: successCount, failed: failureCount }
  }
}

export default TimeOutCycleService
