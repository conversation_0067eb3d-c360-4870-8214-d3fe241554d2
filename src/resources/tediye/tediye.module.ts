import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { TediyeService } from './tediye.service';
import { TediyeController } from './tediye.controller';
import { PrismaModule } from 'src/db/prisma.module';

@Module({
  imports: [HttpModule, ConfigModule, PrismaModule],
  controllers: [TediyeController],
  providers: [TediyeService],
})
export class TediyeModule {}
