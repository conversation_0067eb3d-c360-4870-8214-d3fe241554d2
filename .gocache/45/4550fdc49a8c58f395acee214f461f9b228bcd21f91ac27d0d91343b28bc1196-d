// Code generated by cmd/cgo; DO NOT EDIT.

//line /home/<USER>/go/pkg/mod/github.com/mattn/go-sqlite3@v1.14.31/sqlite3.go:1:1
// Copyright (C) 2019 <PERSON><PERSON><PERSON> <<EMAIL>>.
// Copyright (C) 2018 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
//
// Use of this source code is governed by an MIT-style
// license that can be found in the LICENSE file.

//go:build cgo
// +build cgo

package sqlite3; import _cgo_unsafe "unsafe"

/*
#cgo CFLAGS: -std=gnu99
#cgo CFLAGS: -DSQLITE_ENABLE_RTREE
#cgo CFLAGS: -DSQLITE_THREADSAFE=1
#cgo CFLAGS: -DHAVE_USLEEP=1
#cgo CFLAGS: -DSQLITE_ENABLE_FTS3
#cgo CFLAGS: -DSQLITE_ENABLE_FTS3_PARENTHESIS
#cgo CFLAGS: -DSQLITE_TRACE_SIZE_LIMIT=15
#cgo CFLAGS: -DSQLITE_OMIT_DEPRECATED
#cgo CFLAGS: -DSQLITE_DEFAULT_WAL_SYNCHRONOUS=1
#cgo CFLAGS: -DSQLITE_ENABLE_UPDATE_DELETE_LIMIT
#cgo CFLAGS: -Wno-deprecated-declarations
#cgo openbsd CFLAGS: -I/usr/local/include
#cgo openbsd LDFLAGS: -L/usr/local/lib
#ifndef USE_LIBSQLITE3
#include "sqlite3-binding.h"
#else
#include <sqlite3.h>
#endif
#include <stdlib.h>
#include <string.h>

#ifdef __CYGWIN__
# include <errno.h>
#endif

#ifndef SQLITE_OPEN_READWRITE
# define SQLITE_OPEN_READWRITE 0
#endif

#ifndef SQLITE_OPEN_FULLMUTEX
# define SQLITE_OPEN_FULLMUTEX 0
#endif

#ifndef SQLITE_DETERMINISTIC
# define SQLITE_DETERMINISTIC 0
#endif

#if defined(HAVE_PREAD64) && defined(HAVE_PWRITE64)
# undef USE_PREAD
# undef USE_PWRITE
# define USE_PREAD64 1
# define USE_PWRITE64 1
#elif defined(HAVE_PREAD) && defined(HAVE_PWRITE)
# undef USE_PREAD
# undef USE_PWRITE
# define USE_PREAD64 1
# define USE_PWRITE64 1
#endif

static int
_sqlite3_open_v2(const char *filename, sqlite3 **ppDb, int flags, const char *zVfs) {
#ifdef SQLITE_OPEN_URI
  return sqlite3_open_v2(filename, ppDb, flags | SQLITE_OPEN_URI, zVfs);
#else
  return sqlite3_open_v2(filename, ppDb, flags, zVfs);
#endif
}

static int
_sqlite3_bind_text(sqlite3_stmt *stmt, int n, char *p, int np) {
  return sqlite3_bind_text(stmt, n, p, np, SQLITE_TRANSIENT);
}

static int
_sqlite3_bind_blob(sqlite3_stmt *stmt, int n, void *p, int np) {
  return sqlite3_bind_blob(stmt, n, p, np, SQLITE_TRANSIENT);
}

#include <stdio.h>
#include <stdint.h>

static int
_sqlite3_exec(sqlite3* db, const char* pcmd, long long* rowid, long long* changes)
{
  int rv = sqlite3_exec(db, pcmd, 0, 0, 0);
  *rowid = (long long) sqlite3_last_insert_rowid(db);
  *changes = (long long) sqlite3_changes(db);
  return rv;
}

#ifdef SQLITE_ENABLE_UNLOCK_NOTIFY
extern int _sqlite3_step_blocking(sqlite3_stmt *stmt);
extern int _sqlite3_step_row_blocking(sqlite3_stmt* stmt, long long* rowid, long long* changes);
extern int _sqlite3_prepare_v2_blocking(sqlite3 *db, const char *zSql, int nBytes, sqlite3_stmt **ppStmt, const char **pzTail);

static int
_sqlite3_step_internal(sqlite3_stmt *stmt)
{
  return _sqlite3_step_blocking(stmt);
}

static int
_sqlite3_step_row_internal(sqlite3_stmt* stmt, long long* rowid, long long* changes)
{
  return _sqlite3_step_row_blocking(stmt, rowid, changes);
}

static int
_sqlite3_prepare_v2_internal(sqlite3 *db, const char *zSql, int nBytes, sqlite3_stmt **ppStmt, const char **pzTail)
{
  return _sqlite3_prepare_v2_blocking(db, zSql, nBytes, ppStmt, pzTail);
}

#else
static int
_sqlite3_step_internal(sqlite3_stmt *stmt)
{
  return sqlite3_step(stmt);
}

static int
_sqlite3_step_row_internal(sqlite3_stmt* stmt, long long* rowid, long long* changes)
{
  int rv = sqlite3_step(stmt);
  sqlite3* db = sqlite3_db_handle(stmt);
  *rowid = (long long) sqlite3_last_insert_rowid(db);
  *changes = (long long) sqlite3_changes(db);
  return rv;
}

static int
_sqlite3_prepare_v2_internal(sqlite3 *db, const char *zSql, int nBytes, sqlite3_stmt **ppStmt, const char **pzTail)
{
  return sqlite3_prepare_v2(db, zSql, nBytes, ppStmt, pzTail);
}
#endif

void _sqlite3_result_text(sqlite3_context* ctx, const char* s) {
  sqlite3_result_text(ctx, s, -1, &free);
}

void _sqlite3_result_blob(sqlite3_context* ctx, const void* b, int l) {
  sqlite3_result_blob(ctx, b, l, SQLITE_TRANSIENT);
}


int _sqlite3_create_function(
  sqlite3 *db,
  const char *zFunctionName,
  int nArg,
  int eTextRep,
  uintptr_t pApp,
  void (*xFunc)(sqlite3_context*,int,sqlite3_value**),
  void (*xStep)(sqlite3_context*,int,sqlite3_value**),
  void (*xFinal)(sqlite3_context*)
) {
  return sqlite3_create_function(db, zFunctionName, nArg, eTextRep, (void*) pApp, xFunc, xStep, xFinal);
}

void callbackTrampoline(sqlite3_context*, int, sqlite3_value**);
void stepTrampoline(sqlite3_context*, int, sqlite3_value**);
void doneTrampoline(sqlite3_context*);

int compareTrampoline(void*, int, char*, int, char*);
int commitHookTrampoline(void*);
void rollbackHookTrampoline(void*);
void updateHookTrampoline(void*, int, char*, char*, sqlite3_int64);

int authorizerTrampoline(void*, int, char*, char*, char*, char*);

#ifdef SQLITE_LIMIT_WORKER_THREADS
# define _SQLITE_HAS_LIMIT
# define SQLITE_LIMIT_LENGTH                    0
# define SQLITE_LIMIT_SQL_LENGTH                1
# define SQLITE_LIMIT_COLUMN                    2
# define SQLITE_LIMIT_EXPR_DEPTH                3
# define SQLITE_LIMIT_COMPOUND_SELECT           4
# define SQLITE_LIMIT_VDBE_OP                   5
# define SQLITE_LIMIT_FUNCTION_ARG              6
# define SQLITE_LIMIT_ATTACHED                  7
# define SQLITE_LIMIT_LIKE_PATTERN_LENGTH       8
# define SQLITE_LIMIT_VARIABLE_NUMBER           9
# define SQLITE_LIMIT_TRIGGER_DEPTH            10
# define SQLITE_LIMIT_WORKER_THREADS           11
# else
# define SQLITE_LIMIT_WORKER_THREADS           11
#endif

static int _sqlite3_limit(sqlite3* db, int limitId, int newLimit) {
#ifndef _SQLITE_HAS_LIMIT
  return -1;
#else
  return sqlite3_limit(db, limitId, newLimit);
#endif
}

#if SQLITE_VERSION_NUMBER < 3012000
static int sqlite3_system_errno(sqlite3 *db) {
  return 0;
}
#endif
*/
import _ "unsafe"
import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"fmt"
	"io"
	"net/url"
	"reflect"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"
	"unsafe"
)

// SQLiteTimestampFormats is timestamp formats understood by both this module
// and SQLite.  The first format in the slice will be used when saving time
// values into the database. When parsing a string from a timestamp or datetime
// column, the formats are tried in order.
var SQLiteTimestampFormats = []string{
	// By default, store timestamps with whatever timezone they come with.
	// When parsed, they will be returned with the same timezone.
	"2006-01-02 15:04:05.999999999-07:00",
	"2006-01-02T15:04:05.999999999-07:00",
	"2006-01-02 15:04:05.999999999",
	"2006-01-02T15:04:05.999999999",
	"2006-01-02 15:04:05",
	"2006-01-02T15:04:05",
	"2006-01-02 15:04",
	"2006-01-02T15:04",
	"2006-01-02",
}

const (
	columnDate      string = "date"
	columnDatetime  string = "datetime"
	columnTimestamp string = "timestamp"
)

// This variable can be replaced with -ldflags like below:
// go build -ldflags="-X 'github.com/mattn/go-sqlite3.driverName=my-sqlite3'"
var driverName = "sqlite3"

func init() {
	if driverName != "" {
		sql.Register(driverName, &SQLiteDriver{})
	}
}

// Version returns SQLite library version information.
func Version() (libVersion string, libVersionNumber int, sourceID string) {
	libVersion = ( /*line :260:15*/_Cfunc_GoString /*line :260:24*/)(( /*line :260:26*/_Cfunc_sqlite3_libversion /*line :260:45*/)())
	libVersionNumber = int(( /*line :261:25*/_Cfunc_sqlite3_libversion_number /*line :261:51*/)())
	sourceID = ( /*line :262:13*/_Cfunc_GoString /*line :262:22*/)(( /*line :262:24*/_Cfunc_sqlite3_sourceid /*line :262:41*/)())
	return libVersion, libVersionNumber, sourceID
}

const (
	// used by authorizer and pre_update_hook
	SQLITE_DELETE = ( /*line :268:18*/_Ciconst_SQLITE_DELETE /*line :268:32*/)
	SQLITE_INSERT = ( /*line :269:18*/_Ciconst_SQLITE_INSERT /*line :269:32*/)
	SQLITE_UPDATE = ( /*line :270:18*/_Ciconst_SQLITE_UPDATE /*line :270:32*/)

	// used by authorzier - as return value
	SQLITE_OK     = ( /*line :273:18*/_Ciconst_SQLITE_OK /*line :273:28*/)
	SQLITE_IGNORE = ( /*line :274:18*/_Ciconst_SQLITE_IGNORE /*line :274:32*/)
	SQLITE_DENY   = ( /*line :275:18*/_Ciconst_SQLITE_DENY /*line :275:30*/)

	// different actions query tries to do - passed as argument to authorizer
	SQLITE_CREATE_INDEX        = ( /*line :278:31*/_Ciconst_SQLITE_CREATE_INDEX /*line :278:51*/)
	SQLITE_CREATE_TABLE        = ( /*line :279:31*/_Ciconst_SQLITE_CREATE_TABLE /*line :279:51*/)
	SQLITE_CREATE_TEMP_INDEX   = ( /*line :280:31*/_Ciconst_SQLITE_CREATE_TEMP_INDEX /*line :280:56*/)
	SQLITE_CREATE_TEMP_TABLE   = ( /*line :281:31*/_Ciconst_SQLITE_CREATE_TEMP_TABLE /*line :281:56*/)
	SQLITE_CREATE_TEMP_TRIGGER = ( /*line :282:31*/_Ciconst_SQLITE_CREATE_TEMP_TRIGGER /*line :282:58*/)
	SQLITE_CREATE_TEMP_VIEW    = ( /*line :283:31*/_Ciconst_SQLITE_CREATE_TEMP_VIEW /*line :283:55*/)
	SQLITE_CREATE_TRIGGER      = ( /*line :284:31*/_Ciconst_SQLITE_CREATE_TRIGGER /*line :284:53*/)
	SQLITE_CREATE_VIEW         = ( /*line :285:31*/_Ciconst_SQLITE_CREATE_VIEW /*line :285:50*/)
	SQLITE_CREATE_VTABLE       = ( /*line :286:31*/_Ciconst_SQLITE_CREATE_VTABLE /*line :286:52*/)
	SQLITE_DROP_INDEX          = ( /*line :287:31*/_Ciconst_SQLITE_DROP_INDEX /*line :287:49*/)
	SQLITE_DROP_TABLE          = ( /*line :288:31*/_Ciconst_SQLITE_DROP_TABLE /*line :288:49*/)
	SQLITE_DROP_TEMP_INDEX     = ( /*line :289:31*/_Ciconst_SQLITE_DROP_TEMP_INDEX /*line :289:54*/)
	SQLITE_DROP_TEMP_TABLE     = ( /*line :290:31*/_Ciconst_SQLITE_DROP_TEMP_TABLE /*line :290:54*/)
	SQLITE_DROP_TEMP_TRIGGER   = ( /*line :291:31*/_Ciconst_SQLITE_DROP_TEMP_TRIGGER /*line :291:56*/)
	SQLITE_DROP_TEMP_VIEW      = ( /*line :292:31*/_Ciconst_SQLITE_DROP_TEMP_VIEW /*line :292:53*/)
	SQLITE_DROP_TRIGGER        = ( /*line :293:31*/_Ciconst_SQLITE_DROP_TRIGGER /*line :293:51*/)
	SQLITE_DROP_VIEW           = ( /*line :294:31*/_Ciconst_SQLITE_DROP_VIEW /*line :294:48*/)
	SQLITE_DROP_VTABLE         = ( /*line :295:31*/_Ciconst_SQLITE_DROP_VTABLE /*line :295:50*/)
	SQLITE_PRAGMA              = ( /*line :296:31*/_Ciconst_SQLITE_PRAGMA /*line :296:45*/)
	SQLITE_READ                = ( /*line :297:31*/_Ciconst_SQLITE_READ /*line :297:43*/)
	SQLITE_SELECT              = ( /*line :298:31*/_Ciconst_SQLITE_SELECT /*line :298:45*/)
	SQLITE_TRANSACTION         = ( /*line :299:31*/_Ciconst_SQLITE_TRANSACTION /*line :299:50*/)
	SQLITE_ATTACH              = ( /*line :300:31*/_Ciconst_SQLITE_ATTACH /*line :300:45*/)
	SQLITE_DETACH              = ( /*line :301:31*/_Ciconst_SQLITE_DETACH /*line :301:45*/)
	SQLITE_ALTER_TABLE         = ( /*line :302:31*/_Ciconst_SQLITE_ALTER_TABLE /*line :302:50*/)
	SQLITE_REINDEX             = ( /*line :303:31*/_Ciconst_SQLITE_REINDEX /*line :303:46*/)
	SQLITE_ANALYZE             = ( /*line :304:31*/_Ciconst_SQLITE_ANALYZE /*line :304:46*/)
	SQLITE_FUNCTION            = ( /*line :305:31*/_Ciconst_SQLITE_FUNCTION /*line :305:47*/)
	SQLITE_SAVEPOINT           = ( /*line :306:31*/_Ciconst_SQLITE_SAVEPOINT /*line :306:48*/)
	SQLITE_COPY                = ( /*line :307:31*/_Ciconst_SQLITE_COPY /*line :307:43*/)
	/*SQLITE_RECURSIVE           = C.SQLITE_RECURSIVE*/
)

// Standard File Control Opcodes
// See: https://www.sqlite.org/c3ref/c_fcntl_begin_atomic_write.html
const (
	SQLITE_FCNTL_LOCKSTATE             = int(1)
	SQLITE_FCNTL_GET_LOCKPROXYFILE     = int(2)
	SQLITE_FCNTL_SET_LOCKPROXYFILE     = int(3)
	SQLITE_FCNTL_LAST_ERRNO            = int(4)
	SQLITE_FCNTL_SIZE_HINT             = int(5)
	SQLITE_FCNTL_CHUNK_SIZE            = int(6)
	SQLITE_FCNTL_FILE_POINTER          = int(7)
	SQLITE_FCNTL_SYNC_OMITTED          = int(8)
	SQLITE_FCNTL_WIN32_AV_RETRY        = int(9)
	SQLITE_FCNTL_PERSIST_WAL           = int(10)
	SQLITE_FCNTL_OVERWRITE             = int(11)
	SQLITE_FCNTL_VFSNAME               = int(12)
	SQLITE_FCNTL_POWERSAFE_OVERWRITE   = int(13)
	SQLITE_FCNTL_PRAGMA                = int(14)
	SQLITE_FCNTL_BUSYHANDLER           = int(15)
	SQLITE_FCNTL_TEMPFILENAME          = int(16)
	SQLITE_FCNTL_MMAP_SIZE             = int(18)
	SQLITE_FCNTL_TRACE                 = int(19)
	SQLITE_FCNTL_HAS_MOVED             = int(20)
	SQLITE_FCNTL_SYNC                  = int(21)
	SQLITE_FCNTL_COMMIT_PHASETWO       = int(22)
	SQLITE_FCNTL_WIN32_SET_HANDLE      = int(23)
	SQLITE_FCNTL_WAL_BLOCK             = int(24)
	SQLITE_FCNTL_ZIPVFS                = int(25)
	SQLITE_FCNTL_RBU                   = int(26)
	SQLITE_FCNTL_VFS_POINTER           = int(27)
	SQLITE_FCNTL_JOURNAL_POINTER       = int(28)
	SQLITE_FCNTL_WIN32_GET_HANDLE      = int(29)
	SQLITE_FCNTL_PDB                   = int(30)
	SQLITE_FCNTL_BEGIN_ATOMIC_WRITE    = int(31)
	SQLITE_FCNTL_COMMIT_ATOMIC_WRITE   = int(32)
	SQLITE_FCNTL_ROLLBACK_ATOMIC_WRITE = int(33)
	SQLITE_FCNTL_LOCK_TIMEOUT          = int(34)
	SQLITE_FCNTL_DATA_VERSION          = int(35)
	SQLITE_FCNTL_SIZE_LIMIT            = int(36)
	SQLITE_FCNTL_CKPT_DONE             = int(37)
	SQLITE_FCNTL_RESERVE_BYTES         = int(38)
	SQLITE_FCNTL_CKPT_START            = int(39)
	SQLITE_FCNTL_EXTERNAL_READER       = int(40)
	SQLITE_FCNTL_CKSM_FILE             = int(41)
)

// SQLiteDriver implements driver.Driver.
type SQLiteDriver struct {
	Extensions  []string
	ConnectHook func(*SQLiteConn) error
}

// SQLiteConn implements driver.Conn.
type SQLiteConn struct {
	mu          sync.Mutex
	db          * /*line :365:15*/_Ctype_sqlite3 /*line :365:24*/
	loc         *time.Location
	txlock      string
	funcs       []*functionInfo
	aggregators []*aggInfo
}

// SQLiteTx implements driver.Tx.
type SQLiteTx struct {
	c *SQLiteConn
}

// SQLiteStmt implements driver.Stmt.
type SQLiteStmt struct {
	mu     sync.Mutex
	c      *SQLiteConn
	s      * /*line :381:10*/_Ctype_sqlite3_stmt /*line :381:24*/
	t      string
	closed bool
	cls    bool // True if the statement was created by SQLiteConn.Query
}

// SQLiteResult implements sql.Result.
type SQLiteResult struct {
	id      int64
	changes int64
}

// SQLiteRows implements driver.Rows.
type SQLiteRows struct {
	s        *SQLiteStmt
	nc       int32 // Number of columns
	cls      bool  // True if we need to close the parent statement in Close
	cols     []string
	decltype []string
	ctx      context.Context // no better alternative to pass context into Next() method
	closemu  sync.Mutex
}

type functionInfo struct {
	f                 reflect.Value
	argConverters     []callbackArgConverter
	variadicConverter callbackArgConverter
	retConverter      callbackRetConverter
}

func (fi *functionInfo) Call(ctx * /*line :411:35*/_Ctype_sqlite3_context /*line :411:52*/, argv []* /*line :411:62*/_Ctype_sqlite3_value /*line :411:77*/) {
	args, err := callbackConvertArgs(argv, fi.argConverters, fi.variadicConverter)
	if err != nil {
		callbackError(ctx, err)
		return
	}

	ret := fi.f.Call(args)

	if len(ret) == 2 && ret[1].Interface() != nil {
		callbackError(ctx, ret[1].Interface().(error))
		return
	}

	err = fi.retConverter(ctx, ret[0])
	if err != nil {
		callbackError(ctx, err)
		return
	}
}

type aggInfo struct {
	constructor reflect.Value

	// Active aggregator objects for aggregations in flight. The
	// aggregators are indexed by a counter stored in the aggregation
	// user data space provided by sqlite.
	active map[int64]reflect.Value
	next   int64

	stepArgConverters     []callbackArgConverter
	stepVariadicConverter callbackArgConverter

	doneRetConverter callbackRetConverter
}

func (ai *aggInfo) agg(ctx * /*line :447:29*/_Ctype_sqlite3_context /*line :447:46*/) (int64, reflect.Value, error) {
	aggIdx := (*int64)(func() _cgo_unsafe.Pointer{ _cgo0 := /*line :448:49*/ctx; var _cgo1 _Ctype_int = _Ctype_int /*line :448:59*/(8); _cgoCheckPointer(_cgo0, nil); return /*line :448:63*/_Cfunc_sqlite3_aggregate_context(_cgo0, _cgo1); }())
	if *aggIdx == 0 {
		*aggIdx = ai.next
		ret := ai.constructor.Call(nil)
		if len(ret) == 2 && ret[1].Interface() != nil {
			return 0, reflect.Value{}, ret[1].Interface().(error)
		}
		if ret[0].IsNil() {
			return 0, reflect.Value{}, errors.New("aggregator constructor returned nil state")
		}
		ai.next++
		ai.active[*aggIdx] = ret[0]
	}
	return *aggIdx, ai.active[*aggIdx], nil
}

func (ai *aggInfo) Step(ctx * /*line :464:30*/_Ctype_sqlite3_context /*line :464:47*/, argv []* /*line :464:57*/_Ctype_sqlite3_value /*line :464:72*/) {
	_, agg, err := ai.agg(ctx)
	if err != nil {
		callbackError(ctx, err)
		return
	}

	args, err := callbackConvertArgs(argv, ai.stepArgConverters, ai.stepVariadicConverter)
	if err != nil {
		callbackError(ctx, err)
		return
	}

	ret := agg.MethodByName("Step").Call(args)
	if len(ret) == 1 && ret[0].Interface() != nil {
		callbackError(ctx, ret[0].Interface().(error))
		return
	}
}

func (ai *aggInfo) Done(ctx * /*line :484:30*/_Ctype_sqlite3_context /*line :484:47*/) {
	idx, agg, err := ai.agg(ctx)
	if err != nil {
		callbackError(ctx, err)
		return
	}
	defer func() { delete(ai.active, idx) }()

	ret := agg.MethodByName("Done").Call(nil)
	if len(ret) == 2 && ret[1].Interface() != nil {
		callbackError(ctx, ret[1].Interface().(error))
		return
	}

	err = ai.doneRetConverter(ctx, ret[0])
	if err != nil {
		callbackError(ctx, err)
		return
	}
}

// Commit transaction.
func (tx *SQLiteTx) Commit() error {
	_, err := tx.c.exec(context.Background(), "COMMIT", nil)
	if err != nil {
		// sqlite3 may leave the transaction open in this scenario.
		// However, database/sql considers the transaction complete once we
		// return from Commit() - we must clean up to honour its semantics.
		// We don't know if the ROLLBACK is strictly necessary, but according
		// to sqlite's docs, there is no harm in calling ROLLBACK unnecessarily.
		tx.c.exec(context.Background(), "ROLLBACK", nil)
	}
	return err
}

// Rollback transaction.
func (tx *SQLiteTx) Rollback() error {
	_, err := tx.c.exec(context.Background(), "ROLLBACK", nil)
	return err
}

// RegisterCollation makes a Go function available as a collation.
//
// cmp receives two UTF-8 strings, a and b. The result should be 0 if
// a==b, -1 if a < b, and +1 if a > b.
//
// cmp must always return the same result given the same
// inputs. Additionally, it must have the following properties for all
// strings A, B and C: if A==B then B==A; if A==B and B==C then A==C;
// if A<B then B>A; if A<B and B<C then A<C.
//
// If cmp does not obey these constraints, sqlite3's behavior is
// undefined when the collation is used.
func (c *SQLiteConn) RegisterCollation(name string, cmp func(string, string) int) error {
	handle := newHandle(c, cmp)
	cname := ( /*line :539:11*/_Cfunc_CString /*line :539:19*/)(name)
	defer func() func() { _cgo0 := /*line :540:15*/unsafe.Pointer(cname); return func() { _cgoCheckPointer(_cgo0, nil); /*line :540:37*/_Cfunc_free(_cgo0); }}()()
	rv := func() _Ctype_int{ _cgo0 := /*line :541:35*/c.db; var _cgo1 *_Ctype_char = /*line :541:41*/cname; var _cgo2 _Ctype_int = /*line :541:48*/_Ciconst_SQLITE_UTF8 /*line :541:61*/; _cgo3 := /*line :541:63*/handle; var _cgo4 *[0]byte = /*line :541:71*/(*[0]byte)(unsafe.Pointer(_Cgo_ptr(_Cfpvar_fp_compareTrampoline /*line :541:116*/))); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo3, nil); return /*line :541:119*/_Cfunc_sqlite3_create_collation(_cgo0, _cgo1, _cgo2, _cgo3, _cgo4); }()
	if rv != ( /*line :542:11*/_Ciconst_SQLITE_OK /*line :542:21*/) {
		return c.lastError()
	}
	return nil
}

// RegisterCommitHook sets the commit hook for a connection.
//
// If the callback returns non-zero the transaction will become a rollback.
//
// If there is an existing commit hook for this connection, it will be
// removed. If callback is nil the existing hook (if any) will be removed
// without creating a new one.
func (c *SQLiteConn) RegisterCommitHook(callback func() int) {
	if callback == nil {
		func() _cgo_unsafe.Pointer{ _cgo0 := /*line :557:25*/c.db; var _cgo1 *[0]byte = /*line :557:31*/nil; var _cgo2 _cgo_unsafe.Pointer = /*line :557:36*/nil; _cgoCheckPointer(_cgo0, nil); return /*line :557:40*/_Cfunc_sqlite3_commit_hook(_cgo0, _cgo1, _cgo2); }()
	} else {
		func() _cgo_unsafe.Pointer{ _cgo0 := /*line :559:25*/c.db; var _cgo1 *[0]byte = /*line :559:31*/(*[0]byte)(_Cgo_ptr(_Cfpvar_fp_commitHookTrampoline /*line :559:64*/)); _cgo2 := /*line :559:67*/newHandle(c, callback); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo2, nil); return /*line :559:90*/_Cfunc_sqlite3_commit_hook(_cgo0, _cgo1, _cgo2); }()
	}
}

// RegisterRollbackHook sets the rollback hook for a connection.
//
// If there is an existing rollback hook for this connection, it will be
// removed. If callback is nil the existing hook (if any) will be removed
// without creating a new one.
func (c *SQLiteConn) RegisterRollbackHook(callback func()) {
	if callback == nil {
		func() _cgo_unsafe.Pointer{ _cgo0 := /*line :570:27*/c.db; var _cgo1 *[0]byte = /*line :570:33*/nil; var _cgo2 _cgo_unsafe.Pointer = /*line :570:38*/nil; _cgoCheckPointer(_cgo0, nil); return /*line :570:42*/_Cfunc_sqlite3_rollback_hook(_cgo0, _cgo1, _cgo2); }()
	} else {
		func() _cgo_unsafe.Pointer{ _cgo0 := /*line :572:27*/c.db; var _cgo1 *[0]byte = /*line :572:33*/(*[0]byte)(_Cgo_ptr(_Cfpvar_fp_rollbackHookTrampoline /*line :572:68*/)); _cgo2 := /*line :572:71*/newHandle(c, callback); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo2, nil); return /*line :572:94*/_Cfunc_sqlite3_rollback_hook(_cgo0, _cgo1, _cgo2); }()
	}
}

// RegisterUpdateHook sets the update hook for a connection.
//
// The parameters to the callback are the operation (one of the constants
// SQLITE_INSERT, SQLITE_DELETE, or SQLITE_UPDATE), the database name, the
// table name, and the rowid.
//
// If there is an existing update hook for this connection, it will be
// removed. If callback is nil the existing hook (if any) will be removed
// without creating a new one.
func (c *SQLiteConn) RegisterUpdateHook(callback func(int, string, string, int64)) {
	if callback == nil {
		func() _cgo_unsafe.Pointer{ _cgo0 := /*line :587:25*/c.db; var _cgo1 *[0]byte = /*line :587:31*/nil; var _cgo2 _cgo_unsafe.Pointer = /*line :587:36*/nil; _cgoCheckPointer(_cgo0, nil); return /*line :587:40*/_Cfunc_sqlite3_update_hook(_cgo0, _cgo1, _cgo2); }()
	} else {
		func() _cgo_unsafe.Pointer{ _cgo0 := /*line :589:25*/c.db; var _cgo1 *[0]byte = /*line :589:31*/(*[0]byte)(_Cgo_ptr(_Cfpvar_fp_updateHookTrampoline /*line :589:64*/)); _cgo2 := /*line :589:67*/newHandle(c, callback); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo2, nil); return /*line :589:90*/_Cfunc_sqlite3_update_hook(_cgo0, _cgo1, _cgo2); }()
	}
}

// RegisterAuthorizer sets the authorizer for connection.
//
// The parameters to the callback are the operation (one of the constants
// SQLITE_INSERT, SQLITE_DELETE, or SQLITE_UPDATE), and 1 to 3 arguments,
// depending on operation. More details see:
// https://www.sqlite.org/c3ref/c_alter_table.html
func (c *SQLiteConn) RegisterAuthorizer(callback func(int, string, string, string) int) {
	if callback == nil {
		func() _Ctype_int{ _cgo0 := /*line :601:28*/c.db; var _cgo1 *[0]byte = /*line :601:34*/nil; var _cgo2 _cgo_unsafe.Pointer = /*line :601:39*/nil; _cgoCheckPointer(_cgo0, nil); return /*line :601:43*/_Cfunc_sqlite3_set_authorizer(_cgo0, _cgo1, _cgo2); }()
	} else {
		func() _Ctype_int{ _cgo0 := /*line :603:28*/c.db; var _cgo1 *[0]byte = /*line :603:34*/(*[0]byte)(_Cgo_ptr(_Cfpvar_fp_authorizerTrampoline /*line :603:67*/)); _cgo2 := /*line :603:70*/newHandle(c, callback); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo2, nil); return /*line :603:93*/_Cfunc_sqlite3_set_authorizer(_cgo0, _cgo1, _cgo2); }()
	}
}

// RegisterFunc makes a Go function available as a SQLite function.
//
// The Go function can have arguments of the following types: any
// numeric type except complex, bool, []byte, string and any.
// any arguments are given the direct translation of the SQLite data type:
// int64 for INTEGER, float64 for FLOAT, []byte for BLOB, string for TEXT.
//
// The function can additionally be variadic, as long as the type of
// the variadic argument is one of the above.
//
// If pure is true. SQLite will assume that the function's return
// value depends only on its inputs, and make more aggressive
// optimizations in its queries.
//
// See _example/go_custom_funcs for a detailed example.
func (c *SQLiteConn) RegisterFunc(name string, impl any, pure bool) error {
	var fi functionInfo
	fi.f = reflect.ValueOf(impl)
	t := fi.f.Type()
	if t.Kind() != reflect.Func {
		return errors.New("Non-function passed to RegisterFunc")
	}
	if t.NumOut() != 1 && t.NumOut() != 2 {
		return errors.New("SQLite functions must return 1 or 2 values")
	}
	if t.NumOut() == 2 && !t.Out(1).Implements(reflect.TypeOf((*error)(nil)).Elem()) {
		return errors.New("Second return value of SQLite function must be error")
	}

	numArgs := t.NumIn()
	if t.IsVariadic() {
		numArgs--
	}

	for i := 0; i < numArgs; i++ {
		conv, err := callbackArg(t.In(i))
		if err != nil {
			return err
		}
		fi.argConverters = append(fi.argConverters, conv)
	}

	if t.IsVariadic() {
		conv, err := callbackArg(t.In(numArgs).Elem())
		if err != nil {
			return err
		}
		fi.variadicConverter = conv
		// Pass -1 to sqlite so that it allows any number of
		// arguments. The call helper verifies that the minimum number
		// of arguments is present for variadic functions.
		numArgs = -1
	}

	conv, err := callbackRet(t.Out(0))
	if err != nil {
		return err
	}
	fi.retConverter = conv

	// fi must outlast the database connection, or we'll have dangling pointers.
	c.funcs = append(c.funcs, &fi)

	cname := ( /*line :670:11*/_Cfunc_CString /*line :670:19*/)(name)
	defer func() func() { _cgo0 := /*line :671:15*/unsafe.Pointer(cname); return func() { _cgoCheckPointer(_cgo0, nil); /*line :671:37*/_Cfunc_free(_cgo0); }}()()
	opts := ( /*line :672:10*/_Ciconst_SQLITE_UTF8 /*line :672:22*/)
	if pure {
		opts |= ( /*line :674:11*/_Ciconst_SQLITE_DETERMINISTIC /*line :674:32*/)
	}
	rv := sqlite3CreateFunction(c.db, cname,  /*line :676:43*/_Ctype_int /*line :676:48*/(numArgs),  /*line :676:59*/_Ctype_int /*line :676:64*/(opts), newHandle(c, &fi), ( /*line :676:91*/_Cgo_ptr(_Cfpvar_fp_callbackTrampoline) /*line :676:110*/), nil, nil)
	if rv != ( /*line :677:11*/_Ciconst_SQLITE_OK /*line :677:21*/) {
		return c.lastError()
	}
	return nil
}

func sqlite3CreateFunction(db * /*line :683:32*/_Ctype_sqlite3 /*line :683:41*/, zFunctionName * /*line :683:58*/_Ctype_char /*line :683:64*/, nArg  /*line :683:71*/_Ctype_int /*line :683:76*/, eTextRep  /*line :683:87*/_Ctype_int /*line :683:92*/, pApp unsafe.Pointer, xFunc unsafe.Pointer, xStep unsafe.Pointer, xFinal unsafe.Pointer)  /*line :683:182*/_Ctype_int /*line :683:187*/ {
	return func() _Ctype_int{ _cgo0 := /*line :684:36*/db; var _cgo1 *_Ctype_char = /*line :684:40*/zFunctionName; var _cgo2 _Ctype_int = /*line :684:55*/nArg; var _cgo3 _Ctype_int = /*line :684:61*/eTextRep; var _cgo4 _Ctype_uintptr_t = _Ctype_uintptr_t /*line :684:82*/(uintptr(pApp)); var _cgo5 *[0]byte = /*line :684:99*/(*[0]byte)(xFunc); var _cgo6 *[0]byte = /*line :684:118*/(*[0]byte)(xStep); var _cgo7 *[0]byte = /*line :684:137*/(*[0]byte)(xFinal); _cgoCheckPointer(_cgo0, nil); return /*line :684:156*/_Cfunc__sqlite3_create_function(_cgo0, _cgo1, _cgo2, _cgo3, _cgo4, _cgo5, _cgo6, _cgo7); }()
}

// RegisterAggregator makes a Go type available as a SQLite aggregation function.
//
// Because aggregation is incremental, it's implemented in Go with a
// type that has 2 methods: func Step(values) accumulates one row of
// data into the accumulator, and func Done() ret finalizes and
// returns the aggregate value. "values" and "ret" may be any type
// supported by RegisterFunc.
//
// RegisterAggregator takes as implementation a constructor function
// that constructs an instance of the aggregator type each time an
// aggregation begins. The constructor must return a pointer to a
// type, or an interface that implements Step() and Done().
//
// The constructor function and the Step/Done methods may optionally
// return an error in addition to their other return values.
//
// See _example/go_custom_funcs for a detailed example.
func (c *SQLiteConn) RegisterAggregator(name string, impl any, pure bool) error {
	var ai aggInfo
	ai.constructor = reflect.ValueOf(impl)
	t := ai.constructor.Type()
	if t.Kind() != reflect.Func {
		return errors.New("non-function passed to RegisterAggregator")
	}
	if t.NumOut() != 1 && t.NumOut() != 2 {
		return errors.New("SQLite aggregator constructors must return 1 or 2 values")
	}
	if t.NumOut() == 2 && !t.Out(1).Implements(reflect.TypeOf((*error)(nil)).Elem()) {
		return errors.New("Second return value of SQLite function must be error")
	}
	if t.NumIn() != 0 {
		return errors.New("SQLite aggregator constructors must not have arguments")
	}

	agg := t.Out(0)
	switch agg.Kind() {
	case reflect.Ptr, reflect.Interface:
	default:
		return errors.New("SQlite aggregator constructor must return a pointer object")
	}
	stepFn, found := agg.MethodByName("Step")
	if !found {
		return errors.New("SQlite aggregator doesn't have a Step() function")
	}
	step := stepFn.Type
	if step.NumOut() != 0 && step.NumOut() != 1 {
		return errors.New("SQlite aggregator Step() function must return 0 or 1 values")
	}
	if step.NumOut() == 1 && !step.Out(0).Implements(reflect.TypeOf((*error)(nil)).Elem()) {
		return errors.New("type of SQlite aggregator Step() return value must be error")
	}

	stepNArgs := step.NumIn()
	start := 0
	if agg.Kind() == reflect.Ptr {
		// Skip over the method receiver
		stepNArgs--
		start++
	}
	if step.IsVariadic() {
		stepNArgs--
	}
	for i := start; i < start+stepNArgs; i++ {
		conv, err := callbackArg(step.In(i))
		if err != nil {
			return err
		}
		ai.stepArgConverters = append(ai.stepArgConverters, conv)
	}
	if step.IsVariadic() {
		conv, err := callbackArg(step.In(start + stepNArgs).Elem())
		if err != nil {
			return err
		}
		ai.stepVariadicConverter = conv
		// Pass -1 to sqlite so that it allows any number of
		// arguments. The call helper verifies that the minimum number
		// of arguments is present for variadic functions.
		stepNArgs = -1
	}

	doneFn, found := agg.MethodByName("Done")
	if !found {
		return errors.New("SQlite aggregator doesn't have a Done() function")
	}
	done := doneFn.Type
	doneNArgs := done.NumIn()
	if agg.Kind() == reflect.Ptr {
		// Skip over the method receiver
		doneNArgs--
	}
	if doneNArgs != 0 {
		return errors.New("SQlite aggregator Done() function must have no arguments")
	}
	if done.NumOut() != 1 && done.NumOut() != 2 {
		return errors.New("SQLite aggregator Done() function must return 1 or 2 values")
	}
	if done.NumOut() == 2 && !done.Out(1).Implements(reflect.TypeOf((*error)(nil)).Elem()) {
		return errors.New("second return value of SQLite aggregator Done() function must be error")
	}

	conv, err := callbackRet(done.Out(0))
	if err != nil {
		return err
	}
	ai.doneRetConverter = conv
	ai.active = make(map[int64]reflect.Value)
	ai.next = 1

	// ai must outlast the database connection, or we'll have dangling pointers.
	c.aggregators = append(c.aggregators, &ai)

	cname := ( /*line :799:11*/_Cfunc_CString /*line :799:19*/)(name)
	defer func() func() { _cgo0 := /*line :800:15*/unsafe.Pointer(cname); return func() { _cgoCheckPointer(_cgo0, nil); /*line :800:37*/_Cfunc_free(_cgo0); }}()()
	opts := ( /*line :801:10*/_Ciconst_SQLITE_UTF8 /*line :801:22*/)
	if pure {
		opts |= ( /*line :803:11*/_Ciconst_SQLITE_DETERMINISTIC /*line :803:32*/)
	}
	rv := sqlite3CreateFunction(c.db, cname,  /*line :805:43*/_Ctype_int /*line :805:48*/(stepNArgs),  /*line :805:61*/_Ctype_int /*line :805:66*/(opts), newHandle(c, &ai), nil, ( /*line :805:98*/_Cgo_ptr(_Cfpvar_fp_stepTrampoline) /*line :805:113*/), ( /*line :805:116*/_Cgo_ptr(_Cfpvar_fp_doneTrampoline) /*line :805:131*/))
	if rv != ( /*line :806:11*/_Ciconst_SQLITE_OK /*line :806:21*/) {
		return c.lastError()
	}
	return nil
}

// AutoCommit return which currently auto commit or not.
func (c *SQLiteConn) AutoCommit() bool {
	c.mu.Lock()
	defer c.mu.Unlock()
	return int(func() _Ctype_int{ _cgo0 := /*line :816:38*/c.db; _cgoCheckPointer(_cgo0, nil); return /*line :816:43*/_Cfunc_sqlite3_get_autocommit(_cgo0); }()) != 0
}

func (c *SQLiteConn) lastError() error {
	return lastError(c.db)
}

// Note: may be called with db == nil
func lastError(db * /*line :824:20*/_Ctype_sqlite3 /*line :824:29*/) error {
	rv := func() _Ctype_int{ _cgo0 := /*line :825:26*/db; _cgoCheckPointer(_cgo0, nil); return /*line :825:29*/_Cfunc_sqlite3_errcode(_cgo0); }() // returns SQLITE_NOMEM if db == nil
	if rv == ( /*line :826:11*/_Ciconst_SQLITE_OK /*line :826:21*/) {
		return nil
	}
	extrv := func() _Ctype_int{ _cgo0 := /*line :829:38*/db; _cgoCheckPointer(_cgo0, nil); return /*line :829:41*/_Cfunc_sqlite3_extended_errcode(_cgo0); }()    // returns SQLITE_NOMEM if db == nil
	errStr := ( /*line :830:12*/_Cfunc_GoString /*line :830:21*/)(func() *_Ctype_char{ _cgo0 := /*line :830:40*/db; _cgoCheckPointer(_cgo0, nil); return /*line :830:43*/_Cfunc_sqlite3_errmsg(_cgo0); }()) // returns "out of memory" if db == nil

	// https://www.sqlite.org/c3ref/system_errno.html
	// sqlite3_system_errno is only meaningful if the error code was SQLITE_CANTOPEN,
	// or it was SQLITE_IOERR and the extended code was not SQLITE_IOERR_NOMEM
	var systemErrno syscall.Errno
	if rv == ( /*line :836:11*/_Ciconst_SQLITE_CANTOPEN /*line :836:27*/) || (rv == ( /*line :836:39*/_Ciconst_SQLITE_IOERR /*line :836:52*/) && extrv != ( /*line :836:66*/_Ciconst_SQLITE_IOERR_NOMEM /*line :836:85*/)) {
		systemErrno = syscall.Errno(func() _Ctype_int{ _cgo0 := /*line :837:54*/db; _cgoCheckPointer(_cgo0, nil); return /*line :837:57*/_Cfunc_sqlite3_system_errno(_cgo0); }())
	}

	return Error{
		Code:         ErrNo(rv),
		ExtendedCode: ErrNoExtended(extrv),
		SystemErrno:  systemErrno,
		err:          errStr,
	}
}

// Exec implements Execer.
func (c *SQLiteConn) Exec(query string, args []driver.Value) (driver.Result, error) {
	list := make([]driver.NamedValue, len(args))
	for i, v := range args {
		list[i] = driver.NamedValue{
			Ordinal: i + 1,
			Value:   v,
		}
	}
	return c.exec(context.Background(), query, list)
}

func (c *SQLiteConn) exec(ctx context.Context, query string, args []driver.NamedValue) (driver.Result, error) {
	start := 0
	for {
		s, err := c.prepare(ctx, query)
		if err != nil {
			return nil, err
		}
		var res driver.Result
		if s.(*SQLiteStmt).s != nil {
			stmtArgs := make([]driver.NamedValue, 0, len(args))
			na := s.NumInput()
			if len(args)-start < na {
				s.Close()
				return nil, fmt.Errorf("not enough args to execute query: want %d got %d", na, len(args))
			}
			// consume the number of arguments used in the current
			// statement and append all named arguments not
			// contained therein
			if len(args[start:start+na]) > 0 {
				stmtArgs = append(stmtArgs, args[start:start+na]...)
				for i := range args {
					if (i < start || i >= na) && args[i].Name != "" {
						stmtArgs = append(stmtArgs, args[i])
					}
				}
				for i := range stmtArgs {
					stmtArgs[i].Ordinal = i + 1
				}
			}
			res, err = s.(*SQLiteStmt).exec(ctx, stmtArgs)
			if err != nil && err != driver.ErrSkip {
				s.Close()
				return nil, err
			}
			start += na
		}
		tail := s.(*SQLiteStmt).t
		s.Close()
		if tail == "" {
			if res == nil {
				// https://github.com/mattn/go-sqlite3/issues/963
				res = &SQLiteResult{0, 0}
			}
			return res, nil
		}
		query = tail
	}
}

// Query implements Queryer.
func (c *SQLiteConn) Query(query string, args []driver.Value) (driver.Rows, error) {
	list := make([]driver.NamedValue, len(args))
	for i, v := range args {
		list[i] = driver.NamedValue{
			Ordinal: i + 1,
			Value:   v,
		}
	}
	return c.query(context.Background(), query, list)
}

func (c *SQLiteConn) query(ctx context.Context, query string, args []driver.NamedValue) (driver.Rows, error) {
	start := 0
	for {
		stmtArgs := make([]driver.NamedValue, 0, len(args))
		s, err := c.prepare(ctx, query)
		if err != nil {
			return nil, err
		}
		s.(*SQLiteStmt).cls = true
		na := s.NumInput()
		if len(args)-start < na {
			s.Close()
			return nil, fmt.Errorf("not enough args to execute query: want %d got %d", na, len(args)-start)
		}
		// consume the number of arguments used in the current
		// statement and append all named arguments not contained
		// therein
		stmtArgs = append(stmtArgs, args[start:start+na]...)
		for i := range args {
			if (i < start || i >= na) && args[i].Name != "" {
				stmtArgs = append(stmtArgs, args[i])
			}
		}
		for i := range stmtArgs {
			stmtArgs[i].Ordinal = i + 1
		}
		rows, err := s.(*SQLiteStmt).query(ctx, stmtArgs)
		if err != nil && err != driver.ErrSkip {
			s.Close()
			return rows, err
		}
		start += na
		tail := s.(*SQLiteStmt).t
		if tail == "" {
			return rows, nil
		}
		rows.Close()
		s.Close()
		query = tail
	}
}

// Begin transaction.
func (c *SQLiteConn) Begin() (driver.Tx, error) {
	return c.begin(context.Background())
}

func (c *SQLiteConn) begin(ctx context.Context) (driver.Tx, error) {
	if _, err := c.exec(ctx, c.txlock, nil); err != nil {
		return nil, err
	}
	return &SQLiteTx{c}, nil
}

// Open database and return a new connection.
//
// A pragma can take either zero or one argument.
// The argument is may be either in parentheses or it may be separated from
// the pragma name by an equal sign. The two syntaxes yield identical results.
// In many pragmas, the argument is a boolean. The boolean can be one of:
//
//	1 yes true on
//	0 no false off
//
// You can specify a DSN string using a URI as the filename.
//
//	test.db
//	file:test.db?cache=shared&mode=memory
//	:memory:
//	file::memory:
//
//	mode
//	  Access mode of the database.
//	  https://www.sqlite.org/c3ref/open.html
//	  Values:
//	   - ro
//	   - rw
//	   - rwc
//	   - memory
//
//	cache
//	  SQLite Shared-Cache Mode
//	  https://www.sqlite.org/sharedcache.html
//	  Values:
//	    - shared
//	    - private
//
//	immutable=Boolean
//	  The immutable parameter is a boolean query parameter that indicates
//	  that the database file is stored on read-only media. When immutable is set,
//	  SQLite assumes that the database file cannot be changed,
//	  even by a process with higher privilege,
//	  and so the database is opened read-only and all locking and change detection is disabled.
//	  Caution: Setting the immutable property on a database file that
//	  does in fact change can result in incorrect query results and/or SQLITE_CORRUPT errors.
//
// go-sqlite3 adds the following query parameters to those used by SQLite:
//
//	_loc=XXX
//	  Specify location of time format. It's possible to specify "auto".
//
//	_mutex=XXX
//	  Specify mutex mode. XXX can be "no", "full".
//
//	_txlock=XXX
//	  Specify locking behavior for transactions.  XXX can be "immediate",
//	  "deferred", "exclusive".
//
//	_auto_vacuum=X | _vacuum=X
//	  0 | none - Auto Vacuum disabled
//	  1 | full - Auto Vacuum FULL
//	  2 | incremental - Auto Vacuum Incremental
//
//	_busy_timeout=XXX"| _timeout=XXX
//	  Specify value for sqlite3_busy_timeout.
//
//	_case_sensitive_like=Boolean | _cslike=Boolean
//	  https://www.sqlite.org/pragma.html#pragma_case_sensitive_like
//	  Default or disabled the LIKE operation is case-insensitive.
//	  When enabling this options behaviour of LIKE will become case-sensitive.
//
//	_defer_foreign_keys=Boolean | _defer_fk=Boolean
//	  Defer Foreign Keys until outermost transaction is committed.
//
//	_foreign_keys=Boolean | _fk=Boolean
//	  Enable or disable enforcement of foreign keys.
//
//	_ignore_check_constraints=Boolean
//	  This pragma enables or disables the enforcement of CHECK constraints.
//	  The default setting is off, meaning that CHECK constraints are enforced by default.
//
//	_journal_mode=MODE | _journal=MODE
//	  Set journal mode for the databases associated with the current connection.
//	  https://www.sqlite.org/pragma.html#pragma_journal_mode
//
//	_locking_mode=X | _locking=X
//	  Sets the database connection locking-mode.
//	  The locking-mode is either NORMAL or EXCLUSIVE.
//	  https://www.sqlite.org/pragma.html#pragma_locking_mode
//
//	_query_only=Boolean
//	  The query_only pragma prevents all changes to database files when enabled.
//
//	_recursive_triggers=Boolean | _rt=Boolean
//	  Enable or disable recursive triggers.
//
//	_secure_delete=Boolean|FAST
//	  When secure_delete is on, SQLite overwrites deleted content with zeros.
//	  https://www.sqlite.org/pragma.html#pragma_secure_delete
//
//	_synchronous=X | _sync=X
//	  Change the setting of the "synchronous" flag.
//	  https://www.sqlite.org/pragma.html#pragma_synchronous
//
//	_writable_schema=Boolean
//	  When this pragma is on, the SQLITE_MASTER tables in which database
//	  can be changed using ordinary UPDATE, INSERT, and DELETE statements.
//	  Warning: misuse of this pragma can easily result in a corrupt database file.
func (d *SQLiteDriver) Open(dsn string) (driver.Conn, error) {
	if ( /*line :1080:5*/_Cfunc_sqlite3_threadsafe /*line :1080:24*/)() == 0 {
		return nil, errors.New("sqlite library was not compiled for thread-safe operation")
	}

	var pkey string

	// Options
	var loc *time.Location
	authCreate := false
	authUser := ""
	authPass := ""
	authCrypt := ""
	authSalt := ""
	mutex :=  /*line :1093:11*/_Ctype_int /*line :1093:16*/(( /*line :1093:17*/_Ciconst_SQLITE_OPEN_FULLMUTEX /*line :1093:39*/))
	txlock := "BEGIN"

	// PRAGMA's
	autoVacuum := -1
	busyTimeout := 5000
	caseSensitiveLike := -1
	deferForeignKeys := -1
	foreignKeys := -1
	ignoreCheckConstraints := -1
	var journalMode string
	lockingMode := "NORMAL"
	queryOnly := -1
	recursiveTriggers := -1
	secureDelete := "DEFAULT"
	synchronousMode := "NORMAL"
	writableSchema := -1
	vfsName := ""
	var cacheSize *int64

	pos := strings.IndexRune(dsn, '?')
	if pos >= 1 {
		params, err := url.ParseQuery(dsn[pos+1:])
		if err != nil {
			return nil, err
		}

		// Authentication
		if _, ok := params["_auth"]; ok {
			authCreate = true
		}
		if val := params.Get("_auth_user"); val != "" {
			authUser = val
		}
		if val := params.Get("_auth_pass"); val != "" {
			authPass = val
		}
		if val := params.Get("_auth_crypt"); val != "" {
			authCrypt = val
		}
		if val := params.Get("_auth_salt"); val != "" {
			authSalt = val
		}

		// _loc
		if val := params.Get("_loc"); val != "" {
			switch strings.ToLower(val) {
			case "auto":
				loc = time.Local
			default:
				loc, err = time.LoadLocation(val)
				if err != nil {
					return nil, fmt.Errorf("Invalid _loc: %v: %v", val, err)
				}
			}
		}

		// _mutex
		if val := params.Get("_mutex"); val != "" {
			switch strings.ToLower(val) {
			case "no":
				mutex = ( /*line :1154:13*/_Ciconst_SQLITE_OPEN_NOMUTEX /*line :1154:33*/)
			case "full":
				mutex = ( /*line :1156:13*/_Ciconst_SQLITE_OPEN_FULLMUTEX /*line :1156:35*/)
			default:
				return nil, fmt.Errorf("Invalid _mutex: %v", val)
			}
		}

		// _txlock
		if val := params.Get("_txlock"); val != "" {
			switch strings.ToLower(val) {
			case "immediate":
				txlock = "BEGIN IMMEDIATE"
			case "exclusive":
				txlock = "BEGIN EXCLUSIVE"
			case "deferred":
				txlock = "BEGIN"
			default:
				return nil, fmt.Errorf("Invalid _txlock: %v", val)
			}
		}

		// Auto Vacuum (_vacuum)
		//
		// https://www.sqlite.org/pragma.html#pragma_auto_vacuum
		//
		pkey = "" // Reset pkey
		if _, ok := params["_auto_vacuum"]; ok {
			pkey = "_auto_vacuum"
		}
		if _, ok := params["_vacuum"]; ok {
			pkey = "_vacuum"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToLower(val) {
			case "0", "none":
				autoVacuum = 0
			case "1", "full":
				autoVacuum = 1
			case "2", "incremental":
				autoVacuum = 2
			default:
				return nil, fmt.Errorf("Invalid _auto_vacuum: %v, expecting value of '0 NONE 1 FULL 2 INCREMENTAL'", val)
			}
		}

		// Busy Timeout (_busy_timeout)
		//
		// https://www.sqlite.org/pragma.html#pragma_busy_timeout
		//
		pkey = "" // Reset pkey
		if _, ok := params["_busy_timeout"]; ok {
			pkey = "_busy_timeout"
		}
		if _, ok := params["_timeout"]; ok {
			pkey = "_timeout"
		}
		if val := params.Get(pkey); val != "" {
			iv, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				return nil, fmt.Errorf("Invalid _busy_timeout: %v: %v", val, err)
			}
			busyTimeout = int(iv)
		}

		// Case Sensitive Like (_cslike)
		//
		// https://www.sqlite.org/pragma.html#pragma_case_sensitive_like
		//
		pkey = "" // Reset pkey
		if _, ok := params["_case_sensitive_like"]; ok {
			pkey = "_case_sensitive_like"
		}
		if _, ok := params["_cslike"]; ok {
			pkey = "_cslike"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				caseSensitiveLike = 0
			case "1", "yes", "true", "on":
				caseSensitiveLike = 1
			default:
				return nil, fmt.Errorf("Invalid _case_sensitive_like: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Defer Foreign Keys (_defer_foreign_keys | _defer_fk)
		//
		// https://www.sqlite.org/pragma.html#pragma_defer_foreign_keys
		//
		pkey = "" // Reset pkey
		if _, ok := params["_defer_foreign_keys"]; ok {
			pkey = "_defer_foreign_keys"
		}
		if _, ok := params["_defer_fk"]; ok {
			pkey = "_defer_fk"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				deferForeignKeys = 0
			case "1", "yes", "true", "on":
				deferForeignKeys = 1
			default:
				return nil, fmt.Errorf("Invalid _defer_foreign_keys: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Foreign Keys (_foreign_keys | _fk)
		//
		// https://www.sqlite.org/pragma.html#pragma_foreign_keys
		//
		pkey = "" // Reset pkey
		if _, ok := params["_foreign_keys"]; ok {
			pkey = "_foreign_keys"
		}
		if _, ok := params["_fk"]; ok {
			pkey = "_fk"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				foreignKeys = 0
			case "1", "yes", "true", "on":
				foreignKeys = 1
			default:
				return nil, fmt.Errorf("Invalid _foreign_keys: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Ignore CHECK Constrains (_ignore_check_constraints)
		//
		// https://www.sqlite.org/pragma.html#pragma_ignore_check_constraints
		//
		if val := params.Get("_ignore_check_constraints"); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				ignoreCheckConstraints = 0
			case "1", "yes", "true", "on":
				ignoreCheckConstraints = 1
			default:
				return nil, fmt.Errorf("Invalid _ignore_check_constraints: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Journal Mode (_journal_mode | _journal)
		//
		// https://www.sqlite.org/pragma.html#pragma_journal_mode
		//
		pkey = "" // Reset pkey
		if _, ok := params["_journal_mode"]; ok {
			pkey = "_journal_mode"
		}
		if _, ok := params["_journal"]; ok {
			pkey = "_journal"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToUpper(val) {
			case "DELETE", "TRUNCATE", "PERSIST", "MEMORY", "OFF":
				journalMode = strings.ToUpper(val)
			case "WAL":
				journalMode = strings.ToUpper(val)

				// For WAL Mode set Synchronous Mode to 'NORMAL'
				// See https://www.sqlite.org/pragma.html#pragma_synchronous
				synchronousMode = "NORMAL"
			default:
				return nil, fmt.Errorf("Invalid _journal: %v, expecting value of 'DELETE TRUNCATE PERSIST MEMORY WAL OFF'", val)
			}
		}

		// Locking Mode (_locking)
		//
		// https://www.sqlite.org/pragma.html#pragma_locking_mode
		//
		pkey = "" // Reset pkey
		if _, ok := params["_locking_mode"]; ok {
			pkey = "_locking_mode"
		}
		if _, ok := params["_locking"]; ok {
			pkey = "_locking"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToUpper(val) {
			case "NORMAL", "EXCLUSIVE":
				lockingMode = strings.ToUpper(val)
			default:
				return nil, fmt.Errorf("Invalid _locking_mode: %v, expecting value of 'NORMAL EXCLUSIVE", val)
			}
		}

		// Query Only (_query_only)
		//
		// https://www.sqlite.org/pragma.html#pragma_query_only
		//
		if val := params.Get("_query_only"); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				queryOnly = 0
			case "1", "yes", "true", "on":
				queryOnly = 1
			default:
				return nil, fmt.Errorf("Invalid _query_only: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Recursive Triggers (_recursive_triggers)
		//
		// https://www.sqlite.org/pragma.html#pragma_recursive_triggers
		//
		pkey = "" // Reset pkey
		if _, ok := params["_recursive_triggers"]; ok {
			pkey = "_recursive_triggers"
		}
		if _, ok := params["_rt"]; ok {
			pkey = "_rt"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				recursiveTriggers = 0
			case "1", "yes", "true", "on":
				recursiveTriggers = 1
			default:
				return nil, fmt.Errorf("Invalid _recursive_triggers: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Secure Delete (_secure_delete)
		//
		// https://www.sqlite.org/pragma.html#pragma_secure_delete
		//
		if val := params.Get("_secure_delete"); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				secureDelete = "OFF"
			case "1", "yes", "true", "on":
				secureDelete = "ON"
			case "fast":
				secureDelete = "FAST"
			default:
				return nil, fmt.Errorf("Invalid _secure_delete: %v, expecting boolean value of '0 1 false true no yes off on fast'", val)
			}
		}

		// Synchronous Mode (_synchronous | _sync)
		//
		// https://www.sqlite.org/pragma.html#pragma_synchronous
		//
		pkey = "" // Reset pkey
		if _, ok := params["_synchronous"]; ok {
			pkey = "_synchronous"
		}
		if _, ok := params["_sync"]; ok {
			pkey = "_sync"
		}
		if val := params.Get(pkey); val != "" {
			switch strings.ToUpper(val) {
			case "0", "OFF", "1", "NORMAL", "2", "FULL", "3", "EXTRA":
				synchronousMode = strings.ToUpper(val)
			default:
				return nil, fmt.Errorf("Invalid _synchronous: %v, expecting value of '0 OFF 1 NORMAL 2 FULL 3 EXTRA'", val)
			}
		}

		// Writable Schema (_writeable_schema)
		//
		// https://www.sqlite.org/pragma.html#pragma_writeable_schema
		//
		if val := params.Get("_writable_schema"); val != "" {
			switch strings.ToLower(val) {
			case "0", "no", "false", "off":
				writableSchema = 0
			case "1", "yes", "true", "on":
				writableSchema = 1
			default:
				return nil, fmt.Errorf("Invalid _writable_schema: %v, expecting boolean value of '0 1 false true no yes off on'", val)
			}
		}

		// Cache size (_cache_size)
		//
		// https://sqlite.org/pragma.html#pragma_cache_size
		//
		if val := params.Get("_cache_size"); val != "" {
			iv, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				return nil, fmt.Errorf("Invalid _cache_size: %v: %v", val, err)
			}
			cacheSize = &iv
		}

		if val := params.Get("vfs"); val != "" {
			vfsName = val
		}

		if !strings.HasPrefix(dsn, "file:") {
			dsn = dsn[:pos]
		}
	}

	var db * /*line :1456:10*/_Ctype_sqlite3 /*line :1456:19*/
	name := ( /*line :1457:10*/_Cfunc_CString /*line :1457:18*/)(dsn)
	defer func() func() { _cgo0 := /*line :1458:15*/unsafe.Pointer(name); return func() { _cgoCheckPointer(_cgo0, nil); /*line :1458:36*/_Cfunc_free(_cgo0); }}()()
	var vfs * /*line :1459:11*/_Ctype_char /*line :1459:17*/
	if vfsName != "" {
		vfs = ( /*line :1461:9*/_Cfunc_CString /*line :1461:17*/)(vfsName)
		defer func() func() { _cgo0 := /*line :1462:16*/unsafe.Pointer(vfs); return func() { _cgoCheckPointer(_cgo0, nil); /*line :1462:36*/_Cfunc_free(_cgo0); }}()()
	}
	rv := func() _Ctype_int{ var _cgo0 *_Ctype_char = /*line :1464:27*/name; _cgoBase1 := /*line :1464:33*/&db; _cgo1 := _cgoBase1; var _cgo2 _Ctype_int = /*line :1465:3*/mutex | _Ciconst_SQLITE_OPEN_READWRITE /*line :1465:32*/ | _Ciconst_SQLITE_OPEN_CREATE /*line :1465:53*/; var _cgo3 *_Ctype_char = /*line :1466:3*/vfs; _cgoCheckPointer(_cgoBase1, 0 == 0); return /*line :1466:7*/_Cfunc__sqlite3_open_v2(_cgo0, _cgo1, _cgo2, _cgo3); }()
	if rv != 0 {
		// Save off the error _before_ closing the database.
		// This is safe even if db is nil.
		err := lastError(db)
		if db != nil {
			func() _Ctype_int{ _cgo0 := /*line :1472:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1472:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
		}
		return nil, err
	}
	if db == nil {
		return nil, errors.New("sqlite succeeded without returning a database")
	}

	exec := func(s string) error {
		cs := ( /*line :1481:9*/_Cfunc_CString /*line :1481:17*/)(s)
		rv := func() _Ctype_int{ _cgo0 := /*line :1482:24*/db; var _cgo1 *_Ctype_char = /*line :1482:28*/cs; var _cgo2 *[0]byte = /*line :1482:32*/nil; var _cgo3 _cgo_unsafe.Pointer = /*line :1482:37*/nil; var _cgo4 **_Ctype_char = /*line :1482:42*/nil; _cgoCheckPointer(_cgo0, nil); return /*line :1482:46*/_Cfunc_sqlite3_exec(_cgo0, _cgo1, _cgo2, _cgo3, _cgo4); }()
		func() { _cgo0 := /*line :1483:10*/unsafe.Pointer(cs); _cgoCheckPointer(_cgo0, nil); /*line :1483:29*/_Cfunc_free(_cgo0); }()
		if rv != ( /*line :1484:12*/_Ciconst_SQLITE_OK /*line :1484:22*/) {
			return lastError(db)
		}
		return nil
	}

	// Busy timeout
	if err := exec(fmt.Sprintf("PRAGMA busy_timeout = %d;", busyTimeout)); err != nil {
		func() _Ctype_int{ _cgo0 := /*line :1492:22*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1492:25*/_Cfunc_sqlite3_close_v2(_cgo0); }()
		return nil, err
	}

	// USER AUTHENTICATION
	//
	// User Authentication is always performed even when
	// sqlite_userauth is not compiled in, because without user authentication
	// the authentication is a no-op.
	//
	// Workflow
	//	- Authenticate
	//		ON::SUCCESS		=> Continue
	//		ON::SQLITE_AUTH => Return error and exit Open(...)
	//
	//  - Activate User Authentication
	//		Check if the user wants to activate User Authentication.
	//		If so then first create a temporary AuthConn to the database
	//		This is possible because we are already successfully authenticated.
	//
	//	- Check if `sqlite_user`` table exists
	//		YES				=> Add the provided user from DSN as Admin User and
	//						   activate user authentication.
	//		NO				=> Continue
	//

	// Create connection to SQLite
	conn := &SQLiteConn{db: db, loc: loc, txlock: txlock}

	// Password Cipher has to be registered before authentication
	if len(authCrypt) > 0 {
		switch strings.ToUpper(authCrypt) {
		case "SHA1":
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSHA1, true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSHA1: %s", err)
			}
		case "SSHA1":
			if len(authSalt) == 0 {
				return nil, fmt.Errorf("_auth_crypt=ssha1, requires _auth_salt")
			}
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSSHA1(authSalt), true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSSHA1: %s", err)
			}
		case "SHA256":
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSHA256, true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSHA256: %s", err)
			}
		case "SSHA256":
			if len(authSalt) == 0 {
				return nil, fmt.Errorf("_auth_crypt=ssha256, requires _auth_salt")
			}
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSSHA256(authSalt), true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSSHA256: %s", err)
			}
		case "SHA384":
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSHA384, true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSHA384: %s", err)
			}
		case "SSHA384":
			if len(authSalt) == 0 {
				return nil, fmt.Errorf("_auth_crypt=ssha384, requires _auth_salt")
			}
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSSHA384(authSalt), true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSSHA384: %s", err)
			}
		case "SHA512":
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSHA512, true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSHA512: %s", err)
			}
		case "SSHA512":
			if len(authSalt) == 0 {
				return nil, fmt.Errorf("_auth_crypt=ssha512, requires _auth_salt")
			}
			if err := conn.RegisterFunc("sqlite_crypt", CryptEncoderSSHA512(authSalt), true); err != nil {
				return nil, fmt.Errorf("CryptEncoderSSHA512: %s", err)
			}
		}
	}

	// Preform Authentication
	if err := conn.Authenticate(authUser, authPass); err != nil {
		return nil, err
	}

	// Register: authenticate
	// Authenticate will perform an authentication of the provided username
	// and password against the database.
	//
	// If a database contains the SQLITE_USER table, then the
	// call to Authenticate must be invoked with an
	// appropriate username and password prior to enable read and write
	//access to the database.
	//
	// Return SQLITE_OK on success or SQLITE_ERROR if the username/password
	// combination is incorrect or unknown.
	//
	// If the SQLITE_USER table is not present in the database file, then
	// this interface is a harmless no-op returnning SQLITE_OK.
	if err := conn.RegisterFunc("authenticate", conn.authenticate, true); err != nil {
		return nil, err
	}
	//
	// Register: auth_user_add
	// auth_user_add can be used (by an admin user only)
	// to create a new user. When called on a no-authentication-required
	// database, this routine converts the database into an authentication-
	// required database, automatically makes the added user an
	// administrator, and logs in the current connection as that user.
	// The AuthUserAdd only works for the "main" database, not
	// for any ATTACH-ed databases. Any call to AuthUserAdd by a
	// non-admin user results in an error.
	if err := conn.RegisterFunc("auth_user_add", conn.authUserAdd, true); err != nil {
		return nil, err
	}
	//
	// Register: auth_user_change
	// auth_user_change can be used to change a users
	// login credentials or admin privilege.  Any user can change their own
	// login credentials. Only an admin user can change another users login
	// credentials or admin privilege setting. No user may change their own
	// admin privilege setting.
	if err := conn.RegisterFunc("auth_user_change", conn.authUserChange, true); err != nil {
		return nil, err
	}
	//
	// Register: auth_user_delete
	// auth_user_delete can be used (by an admin user only)
	// to delete a user. The currently logged-in user cannot be deleted,
	// which guarantees that there is always an admin user and hence that
	// the database cannot be converted into a no-authentication-required
	// database.
	if err := conn.RegisterFunc("auth_user_delete", conn.authUserDelete, true); err != nil {
		return nil, err
	}

	// Register: auth_enabled
	// auth_enabled can be used to check if user authentication is enabled
	if err := conn.RegisterFunc("auth_enabled", conn.authEnabled, true); err != nil {
		return nil, err
	}

	// Auto Vacuum
	// Moved auto_vacuum command, the user preference for auto_vacuum needs to be implemented directly after
	// the authentication and before the sqlite_user table gets created if the user
	// decides to activate User Authentication because
	// auto_vacuum needs to be set before any tables are created
	// and activating user authentication creates the internal table `sqlite_user`.
	if autoVacuum > -1 {
		if err := exec(fmt.Sprintf("PRAGMA auto_vacuum = %d;", autoVacuum)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1641:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1641:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Check if user wants to activate User Authentication
	if authCreate {
		// Before going any further, we need to check that the user
		// has provided an username and password within the DSN.
		// We are not allowed to continue.
		if len(authUser) == 0 {
			return nil, fmt.Errorf("Missing '_auth_user' while user authentication was requested with '_auth'")
		}
		if len(authPass) == 0 {
			return nil, fmt.Errorf("Missing '_auth_pass' while user authentication was requested with '_auth'")
		}

		// Check if User Authentication is Enabled
		authExists := conn.AuthEnabled()
		if !authExists {
			if err := conn.AuthUserAdd(authUser, authPass, true); err != nil {
				return nil, err
			}
		}
	}

	// Case Sensitive LIKE
	if caseSensitiveLike > -1 {
		if err := exec(fmt.Sprintf("PRAGMA case_sensitive_like = %d;", caseSensitiveLike)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1670:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1670:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Defer Foreign Keys
	if deferForeignKeys > -1 {
		if err := exec(fmt.Sprintf("PRAGMA defer_foreign_keys = %d;", deferForeignKeys)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1678:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1678:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Foreign Keys
	if foreignKeys > -1 {
		if err := exec(fmt.Sprintf("PRAGMA foreign_keys = %d;", foreignKeys)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1686:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1686:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Ignore CHECK Constraints
	if ignoreCheckConstraints > -1 {
		if err := exec(fmt.Sprintf("PRAGMA ignore_check_constraints = %d;", ignoreCheckConstraints)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1694:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1694:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Journal Mode
	if journalMode != "" {
		if err := exec(fmt.Sprintf("PRAGMA journal_mode = %s;", journalMode)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1702:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1702:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Locking Mode
	// Because the default is NORMAL and this is not changed in this package
	// by using the compile time SQLITE_DEFAULT_LOCKING_MODE this PRAGMA can always be executed
	if err := exec(fmt.Sprintf("PRAGMA locking_mode = %s;", lockingMode)); err != nil {
		func() _Ctype_int{ _cgo0 := /*line :1711:22*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1711:25*/_Cfunc_sqlite3_close_v2(_cgo0); }()
		return nil, err
	}

	// Query Only
	if queryOnly > -1 {
		if err := exec(fmt.Sprintf("PRAGMA query_only = %d;", queryOnly)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1718:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1718:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Recursive Triggers
	if recursiveTriggers > -1 {
		if err := exec(fmt.Sprintf("PRAGMA recursive_triggers = %d;", recursiveTriggers)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1726:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1726:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Secure Delete
	//
	// Because this package can set the compile time flag SQLITE_SECURE_DELETE with a build tag
	// the default value for secureDelete var is 'DEFAULT' this way
	// you can compile with secure_delete 'ON' and disable it for a specific database connection.
	if secureDelete != "DEFAULT" {
		if err := exec(fmt.Sprintf("PRAGMA secure_delete = %s;", secureDelete)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1738:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1738:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Synchronous Mode
	//
	// Because default is NORMAL this statement is always executed
	if err := exec(fmt.Sprintf("PRAGMA synchronous = %s;", synchronousMode)); err != nil {
		conn.Close()
		return nil, err
	}

	// Writable Schema
	if writableSchema > -1 {
		if err := exec(fmt.Sprintf("PRAGMA writable_schema = %d;", writableSchema)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1754:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1754:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	// Cache Size
	if cacheSize != nil {
		if err := exec(fmt.Sprintf("PRAGMA cache_size = %d;", *cacheSize)); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :1762:23*/db; _cgoCheckPointer(_cgo0, nil); return /*line :1762:26*/_Cfunc_sqlite3_close_v2(_cgo0); }()
			return nil, err
		}
	}

	if len(d.Extensions) > 0 {
		if err := conn.loadExtensions(d.Extensions); err != nil {
			conn.Close()
			return nil, err
		}
	}

	if d.ConnectHook != nil {
		if err := d.ConnectHook(conn); err != nil {
			conn.Close()
			return nil, err
		}
	}
	runtime.SetFinalizer(conn, (*SQLiteConn).Close)
	return conn, nil
}

// Close the connection.
func (c *SQLiteConn) Close() error {
	rv := func() _Ctype_int{ _cgo0 := /*line :1786:27*/c.db; _cgoCheckPointer(_cgo0, nil); return /*line :1786:32*/_Cfunc_sqlite3_close_v2(_cgo0); }()
	if rv != ( /*line :1787:11*/_Ciconst_SQLITE_OK /*line :1787:21*/) {
		return c.lastError()
	}
	deleteHandles(c)
	c.mu.Lock()
	c.db = nil
	c.mu.Unlock()
	runtime.SetFinalizer(c, nil)
	return nil
}

func (c *SQLiteConn) dbConnOpen() bool {
	if c == nil {
		return false
	}
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.db != nil
}

// Prepare the query string. Return a new statement.
func (c *SQLiteConn) Prepare(query string) (driver.Stmt, error) {
	return c.prepare(context.Background(), query)
}

func (c *SQLiteConn) prepare(ctx context.Context, query string) (driver.Stmt, error) {
	pquery := ( /*line :1813:12*/_Cfunc_CString /*line :1813:20*/)(query)
	defer func() func() { _cgo0 := /*line :1814:15*/unsafe.Pointer(pquery); return func() { _cgoCheckPointer(_cgo0, nil); /*line :1814:38*/_Cfunc_free(_cgo0); }}()()
	var s * /*line :1815:9*/_Ctype_sqlite3_stmt /*line :1815:23*/
	var tail * /*line :1816:12*/_Ctype_char /*line :1816:18*/
	rv := func() _Ctype_int{ _cgo0 := /*line :1817:39*/c.db; var _cgo1 *_Ctype_char = /*line :1817:45*/pquery; var _cgo2 _Ctype_int = _Ctype_int /*line :1817:58*/(-1); _cgoBase3 := /*line :1817:64*/&s; _cgo3 := _cgoBase3; _cgoBase4 := /*line :1817:68*/&tail; _cgo4 := _cgoBase4; _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgoBase3, 0 == 0); _cgoCheckPointer(_cgoBase4, 0 == 0); return /*line :1817:74*/_Cfunc__sqlite3_prepare_v2_internal(_cgo0, _cgo1, _cgo2, _cgo3, _cgo4); }()
	if rv != ( /*line :1818:11*/_Ciconst_SQLITE_OK /*line :1818:21*/) {
		return nil, c.lastError()
	}
	var t string
	if tail != nil && *tail != '\000' {
		t = strings.TrimSpace(( /*line :1823:25*/_Cfunc_GoString /*line :1823:34*/)(tail))
	}
	ss := &SQLiteStmt{c: c, s: s, t: t}
	runtime.SetFinalizer(ss, (*SQLiteStmt).Close)
	return ss, nil
}

// Run-Time Limit Categories.
// See: http://www.sqlite.org/c3ref/c_limit_attached.html
const (
	SQLITE_LIMIT_LENGTH              = ( /*line :1833:37*/_Ciconst_SQLITE_LIMIT_LENGTH /*line :1833:57*/)
	SQLITE_LIMIT_SQL_LENGTH          = ( /*line :1834:37*/_Ciconst_SQLITE_LIMIT_SQL_LENGTH /*line :1834:61*/)
	SQLITE_LIMIT_COLUMN              = ( /*line :1835:37*/_Ciconst_SQLITE_LIMIT_COLUMN /*line :1835:57*/)
	SQLITE_LIMIT_EXPR_DEPTH          = ( /*line :1836:37*/_Ciconst_SQLITE_LIMIT_EXPR_DEPTH /*line :1836:61*/)
	SQLITE_LIMIT_COMPOUND_SELECT     = ( /*line :1837:37*/_Ciconst_SQLITE_LIMIT_COMPOUND_SELECT /*line :1837:66*/)
	SQLITE_LIMIT_VDBE_OP             = ( /*line :1838:37*/_Ciconst_SQLITE_LIMIT_VDBE_OP /*line :1838:58*/)
	SQLITE_LIMIT_FUNCTION_ARG        = ( /*line :1839:37*/_Ciconst_SQLITE_LIMIT_FUNCTION_ARG /*line :1839:63*/)
	SQLITE_LIMIT_ATTACHED            = ( /*line :1840:37*/_Ciconst_SQLITE_LIMIT_ATTACHED /*line :1840:59*/)
	SQLITE_LIMIT_LIKE_PATTERN_LENGTH = ( /*line :1841:37*/_Ciconst_SQLITE_LIMIT_LIKE_PATTERN_LENGTH /*line :1841:70*/)
	SQLITE_LIMIT_VARIABLE_NUMBER     = ( /*line :1842:37*/_Ciconst_SQLITE_LIMIT_VARIABLE_NUMBER /*line :1842:66*/)
	SQLITE_LIMIT_TRIGGER_DEPTH       = ( /*line :1843:37*/_Ciconst_SQLITE_LIMIT_TRIGGER_DEPTH /*line :1843:64*/)
	SQLITE_LIMIT_WORKER_THREADS      = ( /*line :1844:37*/_Ciconst_SQLITE_LIMIT_WORKER_THREADS /*line :1844:65*/)
)

// GetFilename returns the absolute path to the file containing
// the requested schema. When passed an empty string, it will
// instead use the database's default schema: "main".
// See: sqlite3_db_filename, https://www.sqlite.org/c3ref/db_filename.html
func (c *SQLiteConn) GetFilename(schemaName string) string {
	if schemaName == "" {
		schemaName = "main"
	}
	return ( /*line :1855:9*/_Cfunc_GoString /*line :1855:18*/)(func() _Ctype_sqlite3_filename{ _cgo0 := /*line :1855:42*/c.db; var _cgo1 *_Ctype_char = _Cfunc_CString /*line :1855:57*/(schemaName); _cgoCheckPointer(_cgo0, nil); return /*line :1855:70*/_Cfunc_sqlite3_db_filename(_cgo0, _cgo1); }())
}

// GetLimit returns the current value of a run-time limit.
// See: sqlite3_limit, http://www.sqlite.org/c3ref/limit.html
func (c *SQLiteConn) GetLimit(id int) int {
	return int(func() _Ctype_int{ _cgo0 := /*line :1861:30*/c.db; var _cgo1 _Ctype_int = _Ctype_int /*line :1861:41*/(id); var _cgo2 _Ctype_int = _Ctype_int /*line :1861:52*/(-1); _cgoCheckPointer(_cgo0, nil); return /*line :1861:57*/_Cfunc__sqlite3_limit(_cgo0, _cgo1, _cgo2); }())
}

// SetLimit changes the value of a run-time limits.
// Then this method returns the prior value of the limit.
// See: sqlite3_limit, http://www.sqlite.org/c3ref/limit.html
func (c *SQLiteConn) SetLimit(id int, newVal int) int {
	return int(func() _Ctype_int{ _cgo0 := /*line :1868:30*/c.db; var _cgo1 _Ctype_int = _Ctype_int /*line :1868:41*/(id); var _cgo2 _Ctype_int = _Ctype_int /*line :1868:52*/(newVal); _cgoCheckPointer(_cgo0, nil); return /*line :1868:61*/_Cfunc__sqlite3_limit(_cgo0, _cgo1, _cgo2); }())
}

// SetFileControlInt invokes the xFileControl method on a given database. The
// dbName is the name of the database. It will default to "main" if left blank.
// The op is one of the opcodes prefixed by "SQLITE_FCNTL_". The arg argument
// and return code are both opcode-specific. Please see the SQLite documentation.
//
// This method is not thread-safe as the returned error code can be changed by
// another call if invoked concurrently.
//
// Use SetFileControlInt64 instead if the argument for the opcode is documented
// as a pointer to a sqlite3_int64.
//
// See: sqlite3_file_control, https://www.sqlite.org/c3ref/file_control.html
func (c *SQLiteConn) SetFileControlInt(dbName string, op int, arg int) error {
	if dbName == "" {
		dbName = "main"
	}

	cDBName := ( /*line :1888:13*/_Cfunc_CString /*line :1888:21*/)(dbName)
	defer func() func() { _cgo0 := /*line :1889:15*/unsafe.Pointer(cDBName); return func() { _cgoCheckPointer(_cgo0, nil); /*line :1889:39*/_Cfunc_free(_cgo0); }}()()

	cArg :=  /*line :1891:10*/_Ctype_int /*line :1891:15*/(arg)
	rv := func() _Ctype_int{ _cgo0 := /*line :1892:31*/c.db; var _cgo1 *_Ctype_char = /*line :1892:37*/cDBName; var _cgo2 _Ctype_int = _Ctype_int /*line :1892:51*/(op); _cgoBase3 := /*line :1892:72*/&cArg; _cgo3 := /*line :1892:57*/unsafe.Pointer(_cgoBase3); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgoBase3, 0 == 0); return /*line :1892:79*/_Cfunc_sqlite3_file_control(_cgo0, _cgo1, _cgo2, _cgo3); }()
	if rv != ( /*line :1893:11*/_Ciconst_SQLITE_OK /*line :1893:21*/) {
		return c.lastError()
	}
	return nil
}

// SetFileControlInt64 invokes the xFileControl method on a given database. The
// dbName is the name of the database. It will default to "main" if left blank.
// The op is one of the opcodes prefixed by "SQLITE_FCNTL_". The arg argument
// and return code are both opcode-specific. Please see the SQLite documentation.
//
// This method is not thread-safe as the returned error code can be changed by
// another call if invoked concurrently.
//
// Only use this method if the argument for the opcode is documented as a pointer
// to a sqlite3_int64.
//
// See: sqlite3_file_control, https://www.sqlite.org/c3ref/file_control.html
func (c *SQLiteConn) SetFileControlInt64(dbName string, op int, arg int64) error {
	if dbName == "" {
		dbName = "main"
	}

	cDBName := ( /*line :1916:13*/_Cfunc_CString /*line :1916:21*/)(dbName)
	defer func() func() { _cgo0 := /*line :1917:15*/unsafe.Pointer(cDBName); return func() { _cgoCheckPointer(_cgo0, nil); /*line :1917:39*/_Cfunc_free(_cgo0); }}()()

	cArg :=  /*line :1919:10*/_Ctype_sqlite3_int64 /*line :1919:25*/(arg)
	rv := func() _Ctype_int{ _cgo0 := /*line :1920:31*/c.db; var _cgo1 *_Ctype_char = /*line :1920:37*/cDBName; var _cgo2 _Ctype_int = _Ctype_int /*line :1920:51*/(op); _cgoBase3 := /*line :1920:72*/&cArg; _cgo3 := /*line :1920:57*/unsafe.Pointer(_cgoBase3); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgoBase3, 0 == 0); return /*line :1920:79*/_Cfunc_sqlite3_file_control(_cgo0, _cgo1, _cgo2, _cgo3); }()
	if rv != ( /*line :1921:11*/_Ciconst_SQLITE_OK /*line :1921:21*/) {
		return c.lastError()
	}
	return nil
}

// Close the statement.
func (s *SQLiteStmt) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	if s.closed {
		return nil
	}
	s.closed = true
	if !s.c.dbConnOpen() {
		return errors.New("sqlite statement with already closed database connection")
	}
	rv := func() _Ctype_int{ _cgo0 := /*line :1938:27*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :1938:31*/_Cfunc_sqlite3_finalize(_cgo0); }()
	s.s = nil
	if rv != ( /*line :1940:11*/_Ciconst_SQLITE_OK /*line :1940:21*/) {
		return s.c.lastError()
	}
	s.c = nil
	runtime.SetFinalizer(s, nil)
	return nil
}

// NumInput return a number of parameters.
func (s *SQLiteStmt) NumInput() int {
	return int(func() _Ctype_int{ _cgo0 := /*line :1950:44*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :1950:48*/_Cfunc_sqlite3_bind_parameter_count(_cgo0); }())
}

var placeHolder = []byte{0}

func (s *SQLiteStmt) bind(args []driver.NamedValue) error {
	rv := func() _Ctype_int{ _cgo0 := /*line :1956:24*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :1956:28*/_Cfunc_sqlite3_reset(_cgo0); }()
	if rv != ( /*line :1957:11*/_Ciconst_SQLITE_ROW /*line :1957:22*/) && rv != ( /*line :1957:33*/_Ciconst_SQLITE_OK /*line :1957:43*/) && rv != ( /*line :1957:54*/_Ciconst_SQLITE_DONE /*line :1957:66*/) {
		return s.c.lastError()
	}

	bindIndices := make([][3]int, len(args))
	prefixes := []string{":", "@", "$"}
	for i, v := range args {
		bindIndices[i][0] = args[i].Ordinal
		if v.Name != "" {
			for j := range prefixes {
				cname := ( /*line :1967:14*/_Cfunc_CString /*line :1967:22*/)(prefixes[j] + v.Name)
				bindIndices[i][j] = int(func() _Ctype_int{ _cgo0 := /*line :1968:60*/s.s; var _cgo1 *_Ctype_char = /*line :1968:65*/cname; _cgoCheckPointer(_cgo0, nil); return /*line :1968:71*/_Cfunc_sqlite3_bind_parameter_index(_cgo0, _cgo1); }())
				func() { _cgo0 := /*line :1969:12*/unsafe.Pointer(cname); _cgoCheckPointer(_cgo0, nil); /*line :1969:34*/_Cfunc_free(_cgo0); }()
			}
			args[i].Ordinal = bindIndices[i][0]
		}
	}

	for i, arg := range args {
		for j := range bindIndices[i] {
			if bindIndices[i][j] == 0 {
				continue
			}
			n :=  /*line :1980:9*/_Ctype_int /*line :1980:14*/(bindIndices[i][j])
			switch v := arg.Value.(type) {
			case nil:
				rv = func() _Ctype_int{ _cgo0 := /*line :1983:30*/s.s; var _cgo1 _Ctype_int = /*line :1983:35*/n; _cgoCheckPointer(_cgo0, nil); return /*line :1983:37*/_Cfunc_sqlite3_bind_null(_cgo0, _cgo1); }()
			case string:
				if len(v) == 0 {
					rv = func() _Ctype_int{ _cgo0 := /*line :1986:32*/s.s; var _cgo1 _Ctype_int = /*line :1986:37*/n; var _cgo2 *_Ctype_char = /*line :1986:40*/(*_Ctype_char /*line :1986:48*/)(unsafe.Pointer(&placeHolder[0])); var _cgo3 _Ctype_int = _Ctype_int /*line :1986:89*/(0); _cgoCheckPointer(_cgo0, nil); return /*line :1986:93*/_Cfunc__sqlite3_bind_text(_cgo0, _cgo1, _cgo2, _cgo3); }()
				} else {
					b := []byte(v)
					rv = func() _Ctype_int{ _cgo0 := /*line :1989:32*/s.s; var _cgo1 _Ctype_int = /*line :1989:37*/n; var _cgo2 *_Ctype_char = /*line :1989:40*/(*_Ctype_char /*line :1989:48*/)(unsafe.Pointer(&b[0])); var _cgo3 _Ctype_int = _Ctype_int /*line :1989:79*/(len(b)); _cgoCheckPointer(_cgo0, nil); return /*line :1989:88*/_Cfunc__sqlite3_bind_text(_cgo0, _cgo1, _cgo2, _cgo3); }()
				}
			case int64:
				rv = func() _Ctype_int{ _cgo0 := /*line :1992:31*/s.s; var _cgo1 _Ctype_int = /*line :1992:36*/n; var _cgo2 _Ctype_sqlite3_int64 = _Ctype_sqlite3_int64 /*line :1992:54*/(v); _cgoCheckPointer(_cgo0, nil); return /*line :1992:58*/_Cfunc_sqlite3_bind_int64(_cgo0, _cgo1, _cgo2); }()
			case bool:
				if v {
					rv = func() _Ctype_int{ _cgo0 := /*line :1995:30*/s.s; var _cgo1 _Ctype_int = /*line :1995:35*/n; var _cgo2 _Ctype_int = /*line :1995:38*/1; _cgoCheckPointer(_cgo0, nil); return /*line :1995:40*/_Cfunc_sqlite3_bind_int(_cgo0, _cgo1, _cgo2); }()
				} else {
					rv = func() _Ctype_int{ _cgo0 := /*line :1997:30*/s.s; var _cgo1 _Ctype_int = /*line :1997:35*/n; var _cgo2 _Ctype_int = /*line :1997:38*/0; _cgoCheckPointer(_cgo0, nil); return /*line :1997:40*/_Cfunc_sqlite3_bind_int(_cgo0, _cgo1, _cgo2); }()
				}
			case float64:
				rv = func() _Ctype_int{ _cgo0 := /*line :2000:32*/s.s; var _cgo1 _Ctype_int = /*line :2000:37*/n; var _cgo2 _Ctype_double = _Ctype_double /*line :2000:48*/(v); _cgoCheckPointer(_cgo0, nil); return /*line :2000:52*/_Cfunc_sqlite3_bind_double(_cgo0, _cgo1, _cgo2); }()
			case []byte:
				if v == nil {
					rv = func() _Ctype_int{ _cgo0 := /*line :2003:31*/s.s; var _cgo1 _Ctype_int = /*line :2003:36*/n; _cgoCheckPointer(_cgo0, nil); return /*line :2003:38*/_Cfunc_sqlite3_bind_null(_cgo0, _cgo1); }()
				} else {
					ln := len(v)
					if ln == 0 {
						v = placeHolder
					}
					rv = func() _Ctype_int{ _cgo0 := /*line :2009:32*/s.s; var _cgo1 _Ctype_int = /*line :2009:37*/n; _cgoIndex2 := &/*line :2009:56*/v; _cgo2 := /*line :2009:40*/unsafe.Pointer(&(*_cgoIndex2)[0]); var _cgo3 _Ctype_int = _Ctype_int /*line :2009:68*/(ln); _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgo2, *_cgoIndex2); return /*line :2009:73*/_Cfunc__sqlite3_bind_blob(_cgo0, _cgo1, _cgo2, _cgo3); }()
				}
			case time.Time:
				b := []byte(v.Format(SQLiteTimestampFormats[0]))
				rv = func() _Ctype_int{ _cgo0 := /*line :2013:31*/s.s; var _cgo1 _Ctype_int = /*line :2013:36*/n; var _cgo2 *_Ctype_char = /*line :2013:39*/(*_Ctype_char /*line :2013:47*/)(unsafe.Pointer(&b[0])); var _cgo3 _Ctype_int = _Ctype_int /*line :2013:78*/(len(b)); _cgoCheckPointer(_cgo0, nil); return /*line :2013:87*/_Cfunc__sqlite3_bind_text(_cgo0, _cgo1, _cgo2, _cgo3); }()
			}
			if rv != ( /*line :2015:13*/_Ciconst_SQLITE_OK /*line :2015:23*/) {
				return s.c.lastError()
			}
		}
	}
	return nil
}

// Query the statement with arguments. Return records.
func (s *SQLiteStmt) Query(args []driver.Value) (driver.Rows, error) {
	list := make([]driver.NamedValue, len(args))
	for i, v := range args {
		list[i] = driver.NamedValue{
			Ordinal: i + 1,
			Value:   v,
		}
	}
	return s.query(context.Background(), list)
}

func (s *SQLiteStmt) query(ctx context.Context, args []driver.NamedValue) (driver.Rows, error) {
	if err := s.bind(args); err != nil {
		return nil, err
	}

	rows := &SQLiteRows{
		s:        s,
		nc:       int32(func() _Ctype_int{ _cgo0 := /*line :2042:42*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2042:46*/_Cfunc_sqlite3_column_count(_cgo0); }()),
		cls:      s.cls,
		cols:     nil,
		decltype: nil,
		ctx:      ctx,
	}

	return rows, nil
}

// LastInsertId return last inserted ID.
func (r *SQLiteResult) LastInsertId() (int64, error) {
	return r.id, nil
}

// RowsAffected return how many rows affected.
func (r *SQLiteResult) RowsAffected() (int64, error) {
	return r.changes, nil
}

// Exec execute the statement with arguments. Return result object.
func (s *SQLiteStmt) Exec(args []driver.Value) (driver.Result, error) {
	list := make([]driver.NamedValue, len(args))
	for i, v := range args {
		list[i] = driver.NamedValue{
			Ordinal: i + 1,
			Value:   v,
		}
	}
	return s.exec(context.Background(), list)
}

func isInterruptErr(err error) bool {
	sqliteErr, ok := err.(Error)
	if ok {
		return sqliteErr.Code == ErrInterrupt
	}
	return false
}

// exec executes a query that doesn't return rows. Attempts to honor context timeout.
func (s *SQLiteStmt) exec(ctx context.Context, args []driver.NamedValue) (driver.Result, error) {
	if ctx.Done() == nil {
		return s.execSync(args)
	}

	type result struct {
		r   driver.Result
		err error
	}
	resultCh := make(chan result)
	defer close(resultCh)
	go func() {
		r, err := s.execSync(args)
		resultCh <- result{r, err}
	}()
	var rv result
	select {
	case rv = <-resultCh:
	case <-ctx.Done():
		select {
		case rv = <-resultCh: // no need to interrupt, operation completed in db
		default:
			// this is still racy and can be no-op if executed between sqlite3_* calls in execSync.
			func() { _cgo0 := /*line :2106:24*/s.c.db; _cgoCheckPointer(_cgo0, nil); /*line :2106:31*/_Cfunc_sqlite3_interrupt(_cgo0); }()
			rv = <-resultCh // wait for goroutine completed
			if isInterruptErr(rv.err) {
				return nil, ctx.Err()
			}
		}
	}
	return rv.r, rv.err
}

func (s *SQLiteStmt) execSync(args []driver.NamedValue) (driver.Result, error) {
	if err := s.bind(args); err != nil {
		func() _Ctype_int{ _cgo0 := /*line :2118:19*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2118:23*/_Cfunc_sqlite3_reset(_cgo0); }()
		func() _Ctype_int{ _cgo0 := /*line :2119:28*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2119:32*/_Cfunc_sqlite3_clear_bindings(_cgo0); }()
		return nil, err
	}

	var rowid, changes  /*line :2123:21*/_Ctype_longlong /*line :2123:31*/
	rv := func() _Ctype_int{ _cgo0 := /*line :2124:37*/s.s; var _cgo1 *_Ctype_longlong = /*line :2124:42*/&rowid; var _cgo2 *_Ctype_longlong = /*line :2124:50*/&changes; _cgoCheckPointer(_cgo0, nil); return /*line :2124:59*/_Cfunc__sqlite3_step_row_internal(_cgo0, _cgo1, _cgo2); }()
	if rv != ( /*line :2125:11*/_Ciconst_SQLITE_ROW /*line :2125:22*/) && rv != ( /*line :2125:33*/_Ciconst_SQLITE_OK /*line :2125:43*/) && rv != ( /*line :2125:54*/_Ciconst_SQLITE_DONE /*line :2125:66*/) {
		err := s.c.lastError()
		func() _Ctype_int{ _cgo0 := /*line :2127:19*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2127:23*/_Cfunc_sqlite3_reset(_cgo0); }()
		func() _Ctype_int{ _cgo0 := /*line :2128:28*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2128:32*/_Cfunc_sqlite3_clear_bindings(_cgo0); }()
		return nil, err
	}

	return &SQLiteResult{id: int64(rowid), changes: int64(changes)}, nil
}

// Readonly reports if this statement is considered readonly by SQLite.
//
// See: https://sqlite.org/c3ref/stmt_readonly.html
func (s *SQLiteStmt) Readonly() bool {
	return func() _Ctype_int{ _cgo0 := /*line :2139:33*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2139:37*/_Cfunc_sqlite3_stmt_readonly(_cgo0); }() == 1
}

// Close the rows.
func (rc *SQLiteRows) Close() error {
	rc.closemu.Lock()
	defer rc.closemu.Unlock()
	s := rc.s
	if s == nil {
		return nil
	}
	rc.s = nil // remove reference to SQLiteStmt
	s.mu.Lock()
	if s.closed {
		s.mu.Unlock()
		return nil
	}
	if rc.cls {
		s.mu.Unlock()
		return s.Close()
	}
	rv := func() _Ctype_int{ _cgo0 := /*line :2160:24*/s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2160:28*/_Cfunc_sqlite3_reset(_cgo0); }()
	if rv != ( /*line :2161:11*/_Ciconst_SQLITE_OK /*line :2161:21*/) {
		s.mu.Unlock()
		return s.c.lastError()
	}
	s.mu.Unlock()
	return nil
}

// Columns return column names.
func (rc *SQLiteRows) Columns() []string {
	rc.s.mu.Lock()
	defer rc.s.mu.Unlock()
	if rc.s.s != nil && int(rc.nc) != len(rc.cols) {
		rc.cols = make([]string, rc.nc)
		for i := 0; i < int(rc.nc); i++ {
			rc.cols[i] = ( /*line :2176:17*/_Cfunc_GoString /*line :2176:26*/)(func() *_Ctype_char{ _cgo0 := /*line :2176:50*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2176:63*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2176:67*/_Cfunc_sqlite3_column_name(_cgo0, _cgo1); }())
		}
	}
	return rc.cols
}

func (rc *SQLiteRows) declTypes() []string {
	if rc.s.s != nil && rc.decltype == nil {
		rc.decltype = make([]string, rc.nc)
		for i := 0; i < int(rc.nc); i++ {
			rc.decltype[i] = strings.ToLower(( /*line :2186:37*/_Cfunc_GoString /*line :2186:46*/)(func() *_Ctype_char{ _cgo0 := /*line :2186:74*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2186:87*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2186:91*/_Cfunc_sqlite3_column_decltype(_cgo0, _cgo1); }()))
		}
	}
	return rc.decltype
}

// DeclTypes return column types.
func (rc *SQLiteRows) DeclTypes() []string {
	rc.s.mu.Lock()
	defer rc.s.mu.Unlock()
	return rc.declTypes()
}

// Next move cursor to next. Attempts to honor context timeout from QueryContext call.
func (rc *SQLiteRows) Next(dest []driver.Value) error {
	rc.s.mu.Lock()
	defer rc.s.mu.Unlock()

	if rc.s.closed {
		return io.EOF
	}

	if rc.ctx.Done() == nil {
		return rc.nextSyncLocked(dest)
	}
	resultCh := make(chan error)
	defer close(resultCh)
	go func() {
		resultCh <- rc.nextSyncLocked(dest)
	}()
	select {
	case err := <-resultCh:
		return err
	case <-rc.ctx.Done():
		select {
		case <-resultCh: // no need to interrupt
		default:
			// this is still racy and can be no-op if executed between sqlite3_* calls in nextSyncLocked.
			func() { _cgo0 := /*line :2224:24*/rc.s.c.db; _cgoCheckPointer(_cgo0, nil); /*line :2224:34*/_Cfunc_sqlite3_interrupt(_cgo0); }()
			<-resultCh // ensure goroutine completed
		}
		return rc.ctx.Err()
	}
}

// nextSyncLocked moves cursor to next; must be called with locked mutex.
func (rc *SQLiteRows) nextSyncLocked(dest []driver.Value) error {
	rv := func() _Ctype_int{ _cgo0 := /*line :2233:33*/rc.s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2233:40*/_Cfunc__sqlite3_step_internal(_cgo0); }()
	if rv == ( /*line :2234:11*/_Ciconst_SQLITE_DONE /*line :2234:23*/) {
		return io.EOF
	}
	if rv != ( /*line :2237:11*/_Ciconst_SQLITE_ROW /*line :2237:22*/) {
		rv = func() _Ctype_int{ _cgo0 := /*line :2238:24*/rc.s.s; _cgoCheckPointer(_cgo0, nil); return /*line :2238:31*/_Cfunc_sqlite3_reset(_cgo0); }()
		if rv != ( /*line :2239:12*/_Ciconst_SQLITE_OK /*line :2239:22*/) {
			return rc.s.c.lastError()
		}
		return nil
	}

	rc.declTypes()

	for i := range dest {
		switch func() _Ctype_int{ _cgo0 := /*line :2248:32*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2248:45*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2248:49*/_Cfunc_sqlite3_column_type(_cgo0, _cgo1); }() {
		case ( /*line :2249:8*/_Ciconst_SQLITE_INTEGER /*line :2249:23*/):
			val := int64(func() _Ctype_sqlite3_int64{ _cgo0 := /*line :2250:40*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2250:53*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2250:57*/_Cfunc_sqlite3_column_int64(_cgo0, _cgo1); }())
			switch rc.decltype[i] {
			case columnTimestamp, columnDatetime, columnDate:
				var t time.Time
				// Assume a millisecond unix timestamp if it's 13 digits -- too
				// large to be a reasonable timestamp in seconds.
				if val > 1e12 || val < -1e12 {
					val *= int64(time.Millisecond) // convert ms to nsec
					t = time.Unix(0, val)
				} else {
					t = time.Unix(val, 0)
				}
				t = t.UTC()
				if rc.s.c.loc != nil {
					t = t.In(rc.s.c.loc)
				}
				dest[i] = t
			case "boolean":
				dest[i] = val > 0
			default:
				dest[i] = val
			}
		case ( /*line :2272:8*/_Ciconst_SQLITE_FLOAT /*line :2272:21*/):
			dest[i] = float64(func() _Ctype_double{ _cgo0 := /*line :2273:46*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2273:59*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2273:63*/_Cfunc_sqlite3_column_double(_cgo0, _cgo1); }())
		case ( /*line :2274:8*/_Ciconst_SQLITE_BLOB /*line :2274:20*/):
			p := func() _cgo_unsafe.Pointer{ _cgo0 := /*line :2275:31*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2275:44*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2275:48*/_Cfunc_sqlite3_column_blob(_cgo0, _cgo1); }()
			if p == nil {
				dest[i] = []byte{}
				continue
			}
			n := func() _Ctype_int{ _cgo0 := /*line :2280:32*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2280:45*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2280:49*/_Cfunc_sqlite3_column_bytes(_cgo0, _cgo1); }()
			dest[i] = func() []byte{ _cgo0 := /*line :2281:24*/p; var _cgo1 _Ctype_int = /*line :2281:27*/n; _cgoCheckPointer(_cgo0, nil); return /*line :2281:29*/_Cfunc_GoBytes(_cgo0, _cgo1); }()
		case ( /*line :2282:8*/_Ciconst_SQLITE_NULL /*line :2282:20*/):
			dest[i] = nil
		case ( /*line :2284:8*/_Ciconst_SQLITE_TEXT /*line :2284:20*/):
			var err error
			var timeVal time.Time

			n := int(func() _Ctype_int{ _cgo0 := /*line :2288:36*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2288:49*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2288:53*/_Cfunc_sqlite3_column_bytes(_cgo0, _cgo1); }())
			s := ( /*line :2289:9*/_Cfunc_GoStringN /*line :2289:19*/)((* /*line :2289:23*/_Ctype_char /*line :2289:29*/)(unsafe.Pointer(func() *_Ctype_uchar{ _cgo0 := /*line :2289:68*/rc.s.s; var _cgo1 _Ctype_int = _Ctype_int /*line :2289:81*/(i); _cgoCheckPointer(_cgo0, nil); return /*line :2289:85*/_Cfunc_sqlite3_column_text(_cgo0, _cgo1); }())),  /*line :2289:89*/_Ctype_int /*line :2289:94*/(n))

			switch rc.decltype[i] {
			case columnTimestamp, columnDatetime, columnDate:
				var t time.Time
				s = strings.TrimSuffix(s, "Z")
				for _, format := range SQLiteTimestampFormats {
					if timeVal, err = time.ParseInLocation(format, s, time.UTC); err == nil {
						t = timeVal
						break
					}
				}
				if err != nil {
					// The column is a time value, so return the zero time on parse failure.
					t = time.Time{}
				}
				if rc.s.c.loc != nil {
					t = t.In(rc.s.c.loc)
				}
				dest[i] = t
			default:
				dest[i] = s
			}
		}
	}
	return nil
}
