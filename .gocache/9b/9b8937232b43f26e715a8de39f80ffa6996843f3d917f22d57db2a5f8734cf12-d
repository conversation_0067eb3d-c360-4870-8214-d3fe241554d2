//go:cgo_ldflag "-O2"
//go:cgo_ldflag "-g"
//go:cgo_ldflag "-ldl"
// Code generated by cmd/cgo; DO NOT EDIT.

package sqlite3

import "unsafe"

import "syscall"

import _cgopackage "runtime/cgo"

type _ _cgopackage.Incomplete
var _ syscall.Errno
func _Cgo_ptr(ptr unsafe.Pointer) unsafe.Pointer { return ptr }

//go:linkname _Cgo_always_false runtime.cgoAlwaysFalse
var _Cgo_always_false bool
//go:linkname _Cgo_use runtime.cgoUse
func _Cgo_use(interface{})
//go:linkname _Cgo_no_callback runtime.cgoNoCallback
func _Cgo_no_callback(bool)
type _Ctype__GoBytes_ []byte

type _Ctype__GoString_ string

type _Ctype_char int8

type _Ctype_double float64

type _Ctype_int int32

type _Ctype_intgo = _Ctype_ptrdiff_t

type _Ctype_long int64

type _Ctype_longlong int64

type _Ctype_ptrdiff_t = _Ctype_long

type _Ctype_size_t = _Ctype_ulong

type _Ctype_sqlite3 = _Ctype_struct_sqlite3

type _Ctype_sqlite3_backup = _Ctype_struct_sqlite3_backup

type _Ctype_sqlite3_context = _Ctype_struct_sqlite3_context

type _Ctype_sqlite3_filename *_Ctype_char

type _Ctype_sqlite3_int64 = _Ctype_sqlite_int64

type _Ctype_sqlite3_stmt = _Ctype_struct_sqlite3_stmt

type _Ctype_sqlite3_uint64 = _Ctype_sqlite_uint64

type _Ctype_sqlite3_value = _Ctype_struct_sqlite3_value

type _Ctype_sqlite_int64 = _Ctype_longlong

type _Ctype_sqlite_uint64 = _Ctype_ulonglong

type _Ctype_struct_sqlite3 _cgopackage.Incomplete

type _Ctype_struct_sqlite3_backup _cgopackage.Incomplete

type _Ctype_struct_sqlite3_context _cgopackage.Incomplete

type _Ctype_struct_sqlite3_stmt _cgopackage.Incomplete

type _Ctype_struct_sqlite3_value _cgopackage.Incomplete

type _Ctype_uchar uint8

type _Ctype_uint uint32

type _Ctype_uintptr_t = _Ctype_ulong

type _Ctype_ulong uint64

type _Ctype_ulonglong uint64

type _Ctype_void [0]byte

//go:linkname _cgo_runtime_cgocall runtime.cgocall
func _cgo_runtime_cgocall(unsafe.Pointer, uintptr) int32

//go:linkname _cgoCheckPointer runtime.cgoCheckPointer
//go:noescape
func _cgoCheckPointer(interface{}, interface{})

//go:linkname _cgoCheckResult runtime.cgoCheckResult
//go:noescape
func _cgoCheckResult(interface{})
//go:linkname __cgo_authorizerTrampoline authorizerTrampoline
//go:cgo_import_static authorizerTrampoline
var __cgo_authorizerTrampoline byte
var _Cfpvar_fp_authorizerTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_authorizerTrampoline))
//go:linkname __cgo_callbackTrampoline callbackTrampoline
//go:cgo_import_static callbackTrampoline
var __cgo_callbackTrampoline byte
var _Cfpvar_fp_callbackTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_callbackTrampoline))
//go:linkname __cgo_commitHookTrampoline commitHookTrampoline
//go:cgo_import_static commitHookTrampoline
var __cgo_commitHookTrampoline byte
var _Cfpvar_fp_commitHookTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_commitHookTrampoline))
//go:linkname __cgo_compareTrampoline compareTrampoline
//go:cgo_import_static compareTrampoline
var __cgo_compareTrampoline byte
var _Cfpvar_fp_compareTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_compareTrampoline))
//go:linkname __cgo_doneTrampoline doneTrampoline
//go:cgo_import_static doneTrampoline
var __cgo_doneTrampoline byte
var _Cfpvar_fp_doneTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_doneTrampoline))
//go:linkname __cgo_rollbackHookTrampoline rollbackHookTrampoline
//go:cgo_import_static rollbackHookTrampoline
var __cgo_rollbackHookTrampoline byte
var _Cfpvar_fp_rollbackHookTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_rollbackHookTrampoline))
//go:linkname __cgo_stepTrampoline stepTrampoline
//go:cgo_import_static stepTrampoline
var __cgo_stepTrampoline byte
var _Cfpvar_fp_stepTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_stepTrampoline))
//go:linkname __cgo_updateHookTrampoline updateHookTrampoline
//go:cgo_import_static updateHookTrampoline
var __cgo_updateHookTrampoline byte
var _Cfpvar_fp_updateHookTrampoline unsafe.Pointer = (unsafe.Pointer)(unsafe.Pointer(&__cgo_updateHookTrampoline))
const _Ciconst_SQLITE_ALTER_TABLE = 0x1a
const _Ciconst_SQLITE_ANALYZE = 0x1c
const _Ciconst_SQLITE_ATTACH = 0x18
const _Ciconst_SQLITE_BLOB = 0x4
const _Ciconst_SQLITE_BUSY = 0x5
const _Ciconst_SQLITE_CANTOPEN = 0xe
const _Ciconst_SQLITE_COPY = 0x0
const _Ciconst_SQLITE_CREATE_INDEX = 0x1
const _Ciconst_SQLITE_CREATE_TABLE = 0x2
const _Ciconst_SQLITE_CREATE_TEMP_INDEX = 0x3
const _Ciconst_SQLITE_CREATE_TEMP_TABLE = 0x4
const _Ciconst_SQLITE_CREATE_TEMP_TRIGGER = 0x5
const _Ciconst_SQLITE_CREATE_TEMP_VIEW = 0x6
const _Ciconst_SQLITE_CREATE_TRIGGER = 0x7
const _Ciconst_SQLITE_CREATE_VIEW = 0x8
const _Ciconst_SQLITE_CREATE_VTABLE = 0x1d
const _Ciconst_SQLITE_DELETE = 0x9
const _Ciconst_SQLITE_DENY = 0x1
const _Ciconst_SQLITE_DESERIALIZE_FREEONCLOSE = 0x1
const _Ciconst_SQLITE_DETACH = 0x19
const _Ciconst_SQLITE_DETERMINISTIC = 0x800
const _Ciconst_SQLITE_DONE = 0x65
const _Ciconst_SQLITE_DROP_INDEX = 0xa
const _Ciconst_SQLITE_DROP_TABLE = 0xb
const _Ciconst_SQLITE_DROP_TEMP_INDEX = 0xc
const _Ciconst_SQLITE_DROP_TEMP_TABLE = 0xd
const _Ciconst_SQLITE_DROP_TEMP_TRIGGER = 0xe
const _Ciconst_SQLITE_DROP_TEMP_VIEW = 0xf
const _Ciconst_SQLITE_DROP_TRIGGER = 0x10
const _Ciconst_SQLITE_DROP_VIEW = 0x11
const _Ciconst_SQLITE_DROP_VTABLE = 0x1e
const _Ciconst_SQLITE_FLOAT = 0x2
const _Ciconst_SQLITE_FUNCTION = 0x1f
const _Ciconst_SQLITE_IGNORE = 0x2
const _Ciconst_SQLITE_INSERT = 0x12
const _Ciconst_SQLITE_INTEGER = 0x1
const _Ciconst_SQLITE_IOERR = 0xa
const _Ciconst_SQLITE_IOERR_NOMEM = 0xc0a
const _Ciconst_SQLITE_LIMIT_ATTACHED = 0x7
const _Ciconst_SQLITE_LIMIT_COLUMN = 0x2
const _Ciconst_SQLITE_LIMIT_COMPOUND_SELECT = 0x4
const _Ciconst_SQLITE_LIMIT_EXPR_DEPTH = 0x3
const _Ciconst_SQLITE_LIMIT_FUNCTION_ARG = 0x6
const _Ciconst_SQLITE_LIMIT_LENGTH = 0x0
const _Ciconst_SQLITE_LIMIT_LIKE_PATTERN_LENGTH = 0x8
const _Ciconst_SQLITE_LIMIT_SQL_LENGTH = 0x1
const _Ciconst_SQLITE_LIMIT_TRIGGER_DEPTH = 0xa
const _Ciconst_SQLITE_LIMIT_VARIABLE_NUMBER = 0x9
const _Ciconst_SQLITE_LIMIT_VDBE_OP = 0x5
const _Ciconst_SQLITE_LIMIT_WORKER_THREADS = 0xb
const _Ciconst_SQLITE_LOCKED = 0x6
const _Ciconst_SQLITE_NULL = 0x5
const _Ciconst_SQLITE_OK = 0x0
const _Ciconst_SQLITE_OPEN_CREATE = 0x4
const _Ciconst_SQLITE_OPEN_FULLMUTEX = 0x10000
const _Ciconst_SQLITE_OPEN_NOMUTEX = 0x8000
const _Ciconst_SQLITE_OPEN_READWRITE = 0x2
const _Ciconst_SQLITE_PRAGMA = 0x13
const _Ciconst_SQLITE_READ = 0x14
const _Ciconst_SQLITE_REINDEX = 0x1b
const _Ciconst_SQLITE_ROW = 0x64
const _Ciconst_SQLITE_SAVEPOINT = 0x20
const _Ciconst_SQLITE_SELECT = 0x15
const _Ciconst_SQLITE_TEXT = 0x3
const _Ciconst_SQLITE_TRANSACTION = 0x16
const _Ciconst_SQLITE_UPDATE = 0x17
const _Ciconst_SQLITE_UTF8 = 0x1


// CString converts the Go string s to a C string.
//
// The C string is allocated in the C heap using malloc.
// It is the caller's responsibility to arrange for it to be
// freed, such as by calling C.free (be sure to include stdlib.h
// if C.free is needed).
func _Cfunc_CString(s string) *_Ctype_char {
	if len(s)+1 <= 0 {
		panic("string too large")
	}
	p := _cgo_cmalloc(uint64(len(s)+1))
	sliceHeader := struct {
		p   unsafe.Pointer
		len int
		cap int
	}{p, len(s)+1, len(s)+1}
	b := *(*[]byte)(unsafe.Pointer(&sliceHeader))
	copy(b, s)
	b[len(s)] = 0
	return (*_Ctype_char)(p)
}

//go:linkname _cgo_runtime_gobytes runtime.gobytes
func _cgo_runtime_gobytes(unsafe.Pointer, int) []byte

// GoBytes converts the C data p with explicit length l to a Go []byte.
func _Cfunc_GoBytes(p unsafe.Pointer, l _Ctype_int) []byte {
	return _cgo_runtime_gobytes(p, int(l))
}

//go:linkname _cgo_runtime_gostring runtime.gostring
func _cgo_runtime_gostring(*_Ctype_char) string

// GoString converts the C string p into a Go string.
func _Cfunc_GoString(p *_Ctype_char) string {
	return _cgo_runtime_gostring(p)
}

//go:linkname _cgo_runtime_gostringn runtime.gostringn
func _cgo_runtime_gostringn(*_Ctype_char, int) string

// GoStringN converts the C data p with explicit length l to a Go string.
func _Cfunc_GoStringN(p *_Ctype_char, l _Ctype_int) string {
	return _cgo_runtime_gostringn(p, int(l))
}

func _Cfunc__CMalloc(n _Ctype_size_t) unsafe.Pointer {
	return _cgo_cmalloc(uint64(n))
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob _cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_bind_blob(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int, p2 unsafe.Pointer, p3 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_blob, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text _cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_bind_text(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int, p2 *_Ctype_char, p3 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_bind_text, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function _cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_create_function(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 _Ctype_int, p3 _Ctype_int, p4 _Ctype_uintptr_t, p5 *[0]byte, p6 *[0]byte, p7 *[0]byte) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_create_function, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
		_Cgo_use(p5)
		_Cgo_use(p6)
		_Cgo_use(p7)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_limit
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_limit _cgo_2d4dad7a45a3_Cfunc__sqlite3_limit
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_limit byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_limit = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_limit)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_limit(p0 *_Ctype_struct_sqlite3, p1 _Ctype_int, p2 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_limit, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2 _cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2 byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_open_v2(p0 *_Ctype_char, p1 **_Ctype_struct_sqlite3, p2 _Ctype_int, p3 *_Ctype_char) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_open_v2, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal _cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_prepare_v2_internal(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 _Ctype_int, p3 **_Ctype_struct_sqlite3_stmt, p4 **_Ctype_char) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_prepare_v2_internal, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob _cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_result_blob(p0 *_Ctype_struct_sqlite3_context, p1 unsafe.Pointer, p2 _Ctype_int) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_result_blob, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text _cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_result_text(p0 *_Ctype_struct_sqlite3_context, p1 *_Ctype_char) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_result_text, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal _cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_step_internal(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_step_internal, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal _cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal
var __cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal byte
var _cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal)

//go:cgo_unsafe_args
func _Cfunc__sqlite3_step_row_internal(p0 *_Ctype_struct_sqlite3_stmt, p1 *_Ctype_longlong, p2 *_Ctype_longlong) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__sqlite3_step_row_internal, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_free
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_free _cgo_2d4dad7a45a3_Cfunc_free
var __cgofn__cgo_2d4dad7a45a3_Cfunc_free byte
var _cgo_2d4dad7a45a3_Cfunc_free = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_free)

//go:cgo_unsafe_args
func _Cfunc_free(p0 unsafe.Pointer) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_free, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_my_result_blob
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_my_result_blob _cgo_2d4dad7a45a3_Cfunc_my_result_blob
var __cgofn__cgo_2d4dad7a45a3_Cfunc_my_result_blob byte
var _cgo_2d4dad7a45a3_Cfunc_my_result_blob = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_my_result_blob)

//go:cgo_unsafe_args
func _Cfunc_my_result_blob(p0 *_Ctype_struct_sqlite3_context, p1 unsafe.Pointer, p2 _Ctype_int) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_my_result_blob, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_my_result_text
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_my_result_text _cgo_2d4dad7a45a3_Cfunc_my_result_text
var __cgofn__cgo_2d4dad7a45a3_Cfunc_my_result_text byte
var _cgo_2d4dad7a45a3_Cfunc_my_result_text = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_my_result_text)

//go:cgo_unsafe_args
func _Cfunc_my_result_text(p0 *_Ctype_struct_sqlite3_context, p1 *_Ctype_char, p2 _Ctype_int) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_my_result_text, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context _cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_aggregate_context(p0 *_Ctype_struct_sqlite3_context, p1 _Ctype_int) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_aggregate_context, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_backup_finish(p0 *_Ctype_struct_sqlite3_backup) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_finish, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_backup_init(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 *_Ctype_struct_sqlite3, p3 *_Ctype_char) (r1 *_Ctype_struct_sqlite3_backup) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_init, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_backup_pagecount(p0 *_Ctype_struct_sqlite3_backup) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_pagecount, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_backup_remaining(p0 *_Ctype_struct_sqlite3_backup) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_remaining, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_backup_step(p0 *_Ctype_struct_sqlite3_backup, p1 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_backup_step, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_bind_double(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int, p2 _Ctype_double) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_double, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_bind_int(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int, p2 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64 _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64 byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_bind_int64(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int, p2 _Ctype_sqlite3_int64) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_int64, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_bind_null(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_null, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_bind_parameter_count(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_count, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_bind_parameter_index(p0 *_Ctype_struct_sqlite3_stmt, p1 *_Ctype_char) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_bind_parameter_index, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings _cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_clear_bindings(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_clear_bindings, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2 _cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2 byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_close_v2(p0 *_Ctype_struct_sqlite3) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_close_v2, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_blob(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_blob, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_bytes(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_bytes, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_count(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_count, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_decltype(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_decltype, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_double(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 _Ctype_double) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_double, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64 _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64 byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_int64(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 _Ctype_sqlite3_int64) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_int64, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_name(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_name, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_text(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 *_Ctype_uchar) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_text, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_column_type(p0 *_Ctype_struct_sqlite3_stmt, p1 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_column_type, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook _cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_commit_hook(p0 *_Ctype_struct_sqlite3, p1 *[0]byte, p2 unsafe.Pointer) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_commit_hook, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation _cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_create_collation(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 _Ctype_int, p3 unsafe.Pointer, p4 *[0]byte) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_create_collation, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename _cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_db_filename(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char) (r1 _Ctype_sqlite3_filename) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_db_filename, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize _cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_deserialize(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 *_Ctype_uchar, p3 _Ctype_sqlite3_int64, p4 _Ctype_sqlite3_int64, p5 _Ctype_uint) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_deserialize, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
		_Cgo_use(p5)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension _cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_enable_load_extension(p0 *_Ctype_struct_sqlite3, p1 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_enable_load_extension, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode _cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_errcode(p0 *_Ctype_struct_sqlite3) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_errcode, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg _cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_errmsg(p0 *_Ctype_struct_sqlite3) (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_errmsg, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr _cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_errstr(p0 _Ctype_int) (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_errstr, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_exec
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_exec _cgo_2d4dad7a45a3_Cfunc_sqlite3_exec
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_exec byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_exec = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_exec)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_exec(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 *[0]byte, p3 unsafe.Pointer, p4 **_Ctype_char) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_exec, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode _cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_extended_errcode(p0 *_Ctype_struct_sqlite3) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_extended_errcode, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control _cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_file_control(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 _Ctype_int, p3 unsafe.Pointer) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_file_control, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize _cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_finalize(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_finalize, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_free
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_free _cgo_2d4dad7a45a3_Cfunc_sqlite3_free
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_free byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_free = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_free)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_free(p0 unsafe.Pointer) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_free, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit _cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_get_autocommit(p0 *_Ctype_struct_sqlite3) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_get_autocommit, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt _cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_interrupt(p0 *_Ctype_struct_sqlite3) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_interrupt, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion _cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_libversion() (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion, uintptr(unsafe.Pointer(&r1)))
	if _Cgo_always_false {
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number _cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_libversion_number() (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_libversion_number, uintptr(unsafe.Pointer(&r1)))
	if _Cgo_always_false {
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension _cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_load_extension(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 *_Ctype_char, p3 **_Ctype_char) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_load_extension, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64 _cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64 byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_malloc64(p0 _Ctype_sqlite3_uint64) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_malloc64, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_reset
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_reset _cgo_2d4dad7a45a3_Cfunc_sqlite3_reset
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_reset byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_reset = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_reset)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_reset(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_reset, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_double(p0 *_Ctype_struct_sqlite3_context, p1 _Ctype_double) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_double, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_error(p0 *_Ctype_struct_sqlite3_context, p1 *_Ctype_char, p2 _Ctype_int) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_error_toobig(p0 *_Ctype_struct_sqlite3_context) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_error_toobig, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_int(p0 *_Ctype_struct_sqlite3_context, p1 _Ctype_int) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64 _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64 byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_int64(p0 *_Ctype_struct_sqlite3_context, p1 _Ctype_sqlite3_int64) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_int64, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_null(p0 *_Ctype_struct_sqlite3_context) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_null, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_result_zeroblob(p0 *_Ctype_struct_sqlite3_context, p1 _Ctype_int) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_result_zeroblob, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook _cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_rollback_hook(p0 *_Ctype_struct_sqlite3, p1 *[0]byte, p2 unsafe.Pointer) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_rollback_hook, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize _cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_serialize(p0 *_Ctype_struct_sqlite3, p1 *_Ctype_char, p2 *_Ctype_sqlite3_int64, p3 _Ctype_uint) (r1 *_Ctype_uchar) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_serialize, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer _cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_set_authorizer(p0 *_Ctype_struct_sqlite3, p1 *[0]byte, p2 unsafe.Pointer) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_set_authorizer, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid _cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_sourceid() (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_sourceid, uintptr(unsafe.Pointer(&r1)))
	if _Cgo_always_false {
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly _cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_stmt_readonly(p0 *_Ctype_struct_sqlite3_stmt) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_stmt_readonly, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno _cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_system_errno(p0 *_Ctype_struct_sqlite3) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_system_errno, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe _cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_threadsafe() (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_threadsafe, uintptr(unsafe.Pointer(&r1)))
	if _Cgo_always_false {
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook _cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_update_hook(p0 *_Ctype_struct_sqlite3, p1 *[0]byte, p2 unsafe.Pointer) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_update_hook, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data _cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_user_data(p0 *_Ctype_struct_sqlite3_context) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_user_data, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_value_blob(p0 *_Ctype_struct_sqlite3_value) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_value_blob, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_value_bytes(p0 *_Ctype_struct_sqlite3_value) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_value_bytes, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_value_double(p0 *_Ctype_struct_sqlite3_value) (r1 _Ctype_double) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_value_double, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64 _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64 byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64 = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_value_int64(p0 *_Ctype_struct_sqlite3_value) (r1 _Ctype_sqlite3_int64) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_value_int64, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_value_text(p0 *_Ctype_struct_sqlite3_value) (r1 *_Ctype_uchar) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_value_text, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type
var __cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type byte
var _cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type)

//go:cgo_unsafe_args
func _Cfunc_sqlite3_value_type(p0 *_Ctype_struct_sqlite3_value) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc_sqlite3_value_type, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_export_dynamic callbackTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_callbackTrampoline _cgoexp_2d4dad7a45a3_callbackTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_callbackTrampoline
func _cgoexp_2d4dad7a45a3_callbackTrampoline(a *struct {
		p0 *_Ctype_sqlite3_context
		p1 int
		p2 **_Ctype_sqlite3_value
	}) {
	callbackTrampoline(a.p0, a.p1, a.p2)
}
//go:cgo_export_dynamic stepTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_stepTrampoline _cgoexp_2d4dad7a45a3_stepTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_stepTrampoline
func _cgoexp_2d4dad7a45a3_stepTrampoline(a *struct {
		p0 *_Ctype_sqlite3_context
		p1 _Ctype_int
		p2 **_Ctype_sqlite3_value
	}) {
	stepTrampoline(a.p0, a.p1, a.p2)
}
//go:cgo_export_dynamic doneTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_doneTrampoline _cgoexp_2d4dad7a45a3_doneTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_doneTrampoline
func _cgoexp_2d4dad7a45a3_doneTrampoline(a *struct {
		p0 *_Ctype_sqlite3_context
	}) {
	doneTrampoline(a.p0)
}
//go:cgo_export_dynamic compareTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_compareTrampoline _cgoexp_2d4dad7a45a3_compareTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_compareTrampoline
func _cgoexp_2d4dad7a45a3_compareTrampoline(a *struct {
		p0 unsafe.Pointer
		p1 _Ctype_int
		p2 *_Ctype_char
		p3 _Ctype_int
		p4 *_Ctype_char
		r0 _Ctype_int
	}) {
	a.r0 = compareTrampoline(a.p0, a.p1, a.p2, a.p3, a.p4)
}
//go:cgo_export_dynamic commitHookTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_commitHookTrampoline _cgoexp_2d4dad7a45a3_commitHookTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_commitHookTrampoline
func _cgoexp_2d4dad7a45a3_commitHookTrampoline(a *struct {
		p0 unsafe.Pointer
		r0 int
	}) {
	a.r0 = commitHookTrampoline(a.p0)
}
//go:cgo_export_dynamic rollbackHookTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_rollbackHookTrampoline _cgoexp_2d4dad7a45a3_rollbackHookTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_rollbackHookTrampoline
func _cgoexp_2d4dad7a45a3_rollbackHookTrampoline(a *struct {
		p0 unsafe.Pointer
	}) {
	rollbackHookTrampoline(a.p0)
}
//go:cgo_export_dynamic updateHookTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_updateHookTrampoline _cgoexp_2d4dad7a45a3_updateHookTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_updateHookTrampoline
func _cgoexp_2d4dad7a45a3_updateHookTrampoline(a *struct {
		p0 unsafe.Pointer
		p1 int
		p2 *_Ctype_char
		p3 *_Ctype_char
		p4 int64
	}) {
	updateHookTrampoline(a.p0, a.p1, a.p2, a.p3, a.p4)
}
//go:cgo_export_dynamic authorizerTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_authorizerTrampoline _cgoexp_2d4dad7a45a3_authorizerTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_authorizerTrampoline
func _cgoexp_2d4dad7a45a3_authorizerTrampoline(a *struct {
		p0 unsafe.Pointer
		p1 int
		p2 *_Ctype_char
		p3 *_Ctype_char
		p4 *_Ctype_char
		r0 int
	}) {
	a.r0 = authorizerTrampoline(a.p0, a.p1, a.p2, a.p3, a.p4)
}
//go:cgo_export_dynamic preUpdateHookTrampoline
//go:linkname _cgoexp_2d4dad7a45a3_preUpdateHookTrampoline _cgoexp_2d4dad7a45a3_preUpdateHookTrampoline
//go:cgo_export_static _cgoexp_2d4dad7a45a3_preUpdateHookTrampoline
func _cgoexp_2d4dad7a45a3_preUpdateHookTrampoline(a *struct {
		p0 unsafe.Pointer
		p1 uintptr
		p2 int
		p3 *_Ctype_char
		p4 *_Ctype_char
		p5 int64
		p6 int64
	}) {
	preUpdateHookTrampoline(a.p0, a.p1, a.p2, a.p3, a.p4, a.p5, a.p6)
}

//go:cgo_import_static _cgo_2d4dad7a45a3_Cfunc__Cmalloc
//go:linkname __cgofn__cgo_2d4dad7a45a3_Cfunc__Cmalloc _cgo_2d4dad7a45a3_Cfunc__Cmalloc
var __cgofn__cgo_2d4dad7a45a3_Cfunc__Cmalloc byte
var _cgo_2d4dad7a45a3_Cfunc__Cmalloc = unsafe.Pointer(&__cgofn__cgo_2d4dad7a45a3_Cfunc__Cmalloc)

//go:linkname runtime_throw runtime.throw
func runtime_throw(string)

//go:cgo_unsafe_args
func _cgo_cmalloc(p0 uint64) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_2d4dad7a45a3_Cfunc__Cmalloc, uintptr(unsafe.Pointer(&p0)))
	if r1 == nil {
		runtime_throw("runtime: C malloc failed")
	}
	return
}
