import { Controller, Post, Logger } from '@nestjs/common';
import { TediyeService } from './tediye.service';

@Controller('tediye')
export class TediyeController {
  private readonly logger = new Logger(TediyeController.name);

  constructor(private readonly tediyeService: TediyeService) {}

  @Post('create')
  async createTediye() {
    this.logger.log('Tediye oluşturma isteği alındı');
    return await this.tediyeService.createTediye();
  }
}
