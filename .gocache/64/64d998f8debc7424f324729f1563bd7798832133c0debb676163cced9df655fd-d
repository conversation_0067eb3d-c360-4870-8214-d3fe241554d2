// Code generated by cmd/cgo; DO NOT EDIT.

//line /home/<USER>/go/pkg/mod/github.com/mattn/go-sqlite3@v1.14.31/sqlite3_load_extension.go:1:1
// Copyright (C) 2019 <PERSON><PERSON><PERSON> <<EMAIL>>.
//
// Use of this source code is governed by an MIT-style
// license that can be found in the LICENSE file.

//go:build !sqlite_omit_load_extension
// +build !sqlite_omit_load_extension

package sqlite3

/*
#ifndef USE_LIBSQLITE3
#include "sqlite3-binding.h"
#else
#include <sqlite3.h>
#endif
#include <stdlib.h>
*/
import _ "unsafe"
import (
	"errors"
	"unsafe"
)

func (c *SQLiteConn) loadExtensions(extensions []string) error {
	rv := func() _Ctype_int{ _cgo0 := /*line :26:40*/c.db; var _cgo1 _Ctype_int = /*line :26:46*/1; _cgoCheckPointer(_cgo0, nil); return /*line :26:48*/_Cfunc_sqlite3_enable_load_extension(_cgo0, _cgo1); }()
	if rv != ( /*line :27:11*/_Ciconst_SQLITE_OK /*line :27:21*/) {
		return errors.New(( /*line :28:21*/_Cfunc_GoString /*line :28:30*/)(func() *_Ctype_char{ _cgo0 := /*line :28:49*/c.db; _cgoCheckPointer(_cgo0, nil); return /*line :28:54*/_Cfunc_sqlite3_errmsg(_cgo0); }()))
	}

	for _, extension := range extensions {
		if err := c.loadExtension(extension, nil); err != nil {
			func() _Ctype_int{ _cgo0 := /*line :33:36*/c.db; var _cgo1 _Ctype_int = /*line :33:42*/0; _cgoCheckPointer(_cgo0, nil); return /*line :33:44*/_Cfunc_sqlite3_enable_load_extension(_cgo0, _cgo1); }()
			return err
		}
	}

	rv = func() _Ctype_int{ _cgo0 := /*line :38:39*/c.db; var _cgo1 _Ctype_int = /*line :38:45*/0; _cgoCheckPointer(_cgo0, nil); return /*line :38:47*/_Cfunc_sqlite3_enable_load_extension(_cgo0, _cgo1); }()
	if rv != ( /*line :39:11*/_Ciconst_SQLITE_OK /*line :39:21*/) {
		return errors.New(( /*line :40:21*/_Cfunc_GoString /*line :40:30*/)(func() *_Ctype_char{ _cgo0 := /*line :40:49*/c.db; _cgoCheckPointer(_cgo0, nil); return /*line :40:54*/_Cfunc_sqlite3_errmsg(_cgo0); }()))
	}

	return nil
}

// LoadExtension load the sqlite3 extension.
func (c *SQLiteConn) LoadExtension(lib string, entry string) error {
	rv := func() _Ctype_int{ _cgo0 := /*line :48:40*/c.db; var _cgo1 _Ctype_int = /*line :48:46*/1; _cgoCheckPointer(_cgo0, nil); return /*line :48:48*/_Cfunc_sqlite3_enable_load_extension(_cgo0, _cgo1); }()
	if rv != ( /*line :49:11*/_Ciconst_SQLITE_OK /*line :49:21*/) {
		return errors.New(( /*line :50:21*/_Cfunc_GoString /*line :50:30*/)(func() *_Ctype_char{ _cgo0 := /*line :50:49*/c.db; _cgoCheckPointer(_cgo0, nil); return /*line :50:54*/_Cfunc_sqlite3_errmsg(_cgo0); }()))
	}

	if err := c.loadExtension(lib, &entry); err != nil {
		func() _Ctype_int{ _cgo0 := /*line :54:35*/c.db; var _cgo1 _Ctype_int = /*line :54:41*/0; _cgoCheckPointer(_cgo0, nil); return /*line :54:43*/_Cfunc_sqlite3_enable_load_extension(_cgo0, _cgo1); }()
		return err
	}

	rv = func() _Ctype_int{ _cgo0 := /*line :58:39*/c.db; var _cgo1 _Ctype_int = /*line :58:45*/0; _cgoCheckPointer(_cgo0, nil); return /*line :58:47*/_Cfunc_sqlite3_enable_load_extension(_cgo0, _cgo1); }()
	if rv != ( /*line :59:11*/_Ciconst_SQLITE_OK /*line :59:21*/) {
		return errors.New(( /*line :60:21*/_Cfunc_GoString /*line :60:30*/)(func() *_Ctype_char{ _cgo0 := /*line :60:49*/c.db; _cgoCheckPointer(_cgo0, nil); return /*line :60:54*/_Cfunc_sqlite3_errmsg(_cgo0); }()))
	}

	return nil
}

func (c *SQLiteConn) loadExtension(lib string, entry *string) error {
	clib := ( /*line :67:10*/_Cfunc_CString /*line :67:18*/)(lib)
	defer func() func() { _cgo0 := /*line :68:15*/unsafe.Pointer(clib); return func() { _cgoCheckPointer(_cgo0, nil); /*line :68:36*/_Cfunc_free(_cgo0); }}()()

	var centry * /*line :70:14*/_Ctype_char /*line :70:20*/
	if entry != nil {
		centry = ( /*line :72:12*/_Cfunc_CString /*line :72:20*/)(*entry)
		defer func() func() { _cgo0 := /*line :73:16*/unsafe.Pointer(centry); return func() { _cgoCheckPointer(_cgo0, nil); /*line :73:39*/_Cfunc_free(_cgo0); }}()()
	}

	var errMsg * /*line :76:14*/_Ctype_char /*line :76:20*/
	defer func() func() { _cgo0 := /*line :77:23*/unsafe.Pointer(errMsg); return func() { _cgoCheckPointer(_cgo0, nil); /*line :77:46*/_Cfunc_sqlite3_free(_cgo0); }}()()

	rv := func() _Ctype_int{ _cgo0 := /*line :79:33*/c.db; var _cgo1 *_Ctype_char = /*line :79:39*/clib; var _cgo2 *_Ctype_char = /*line :79:45*/centry; _cgoBase3 := /*line :79:53*/&errMsg; _cgo3 := _cgoBase3; _cgoCheckPointer(_cgo0, nil); _cgoCheckPointer(_cgoBase3, 0 == 0); return /*line :79:61*/_Cfunc_sqlite3_load_extension(_cgo0, _cgo1, _cgo2, _cgo3); }()
	if rv != ( /*line :80:11*/_Ciconst_SQLITE_OK /*line :80:21*/) {
		return errors.New(( /*line :81:21*/_Cfunc_GoString /*line :81:30*/)(errMsg))
	}

	return nil
}
