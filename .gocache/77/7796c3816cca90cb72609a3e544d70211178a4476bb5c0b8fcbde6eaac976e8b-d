// Code generated by cmd/cgo; DO NOT EDIT.

//line /usr/lib/go-1.22/src/net/cgo_resnew.go:1:1
// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build cgo && !netgo && ((linux && !android) || netbsd || solaris)

package net

/*
#include <sys/types.h>
#include <sys/socket.h>

#include <netdb.h>
*/
import _ "unsafe"

import "unsafe"

func cgoNameinfoPTR(b []byte, sa * /*line :19:35*/_Ctype_struct_sockaddr /*line :19:52*/, salen  /*line :19:60*/_Ctype_socklen_t /*line :19:71*/) (int, error) {
	gerrno, err := ( /*line :20:17*/_C2func_getnameinfo /*line :20:29*/)(sa, salen, (* /*line :20:44*/_Ctype_char /*line :20:50*/)(unsafe.Pointer(&b[0])),  /*line :20:76*/_Ctype_socklen_t /*line :20:87*/(len(b)), nil, 0, ( /*line :20:105*/_Ciconst_NI_NAMEREQD /*line :20:117*/))
	return int(gerrno), err
}
