// Code generated by cmd/cgo; DO NOT EDIT.

//line /usr/lib/go-1.22/src/os/user/getgrouplist_unix.go:1:1
// Copyright 2016 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build cgo && !osusergo && (dragonfly || freebsd || (!android && linux) || netbsd || openbsd || (solaris && !illumos))

package user

/*
#include <unistd.h>
#include <sys/types.h>
#include <grp.h>

static int mygetgrouplist(const char* user, gid_t group, gid_t* groups, int* ngroups) {
	return getgrouplist(user, group, groups, ngroups);
}
*/
import _ "unsafe"

func getGroupList(name *_C_char, userGID _C_gid_t, gids *_C_gid_t, n *_C_int) _C_int {
	return ( /*line :21:9*/_Cfunc_mygetgrouplist /*line :21:24*/)(name, userGID, gids, n)
}
