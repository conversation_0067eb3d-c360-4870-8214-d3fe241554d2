//go:cgo_ldflag "-O2"
//go:cgo_ldflag "-g"
//go:cgo_ldflag "-lresolv"
// Code generated by cmd/cgo; DO NOT EDIT.

package net

import "unsafe"

import "syscall"

import _cgopackage "runtime/cgo"

type _ _cgopackage.Incomplete
var _ syscall.Errno
func _Cgo_ptr(ptr unsafe.Pointer) unsafe.Pointer { return ptr }

//go:linkname _Cgo_always_false runtime.cgoAlwaysFalse
var _Cgo_always_false bool
//go:linkname _Cgo_use runtime.cgoUse
func _Cgo_use(interface{})
//go:linkname _Cgo_no_callback runtime.cgoNoCallback
func _Cgo_no_callback(bool)
type _Ctype__GoString_ string

type _Ctype___socklen_t = _Ctype_uint

type _Ctype_char int8

type _Ctype_int int32

type _Ctype_intgo = _Ctype_ptrdiff_t

type _Ctype_long int64

type _Ctype_ptrdiff_t = _Ctype_long

type _Ctype_sa_family_t = _Ctype_ushort

type _Ctype_size_t = _Ctype_ulong

type _Ctype_socklen_t = _Ctype___socklen_t

type _Ctype_struct_addrinfo struct {
	ai_flags	_Ctype_int
	ai_family	_Ctype_int
	ai_socktype	_Ctype_int
	ai_protocol	_Ctype_int
	ai_addrlen	_Ctype_socklen_t
	ai_addr		*_Ctype_struct_sockaddr
	ai_canonname	*_Ctype_char
	ai_next		*_Ctype_struct_addrinfo
}

type _Ctype_struct_sockaddr struct {
	sa_family	_Ctype_sa_family_t
	sa_data		[14]_Ctype_char
}

type _Ctype_uchar uint8

type _Ctype_uint uint32

type _Ctype_ulong uint64

type _Ctype_ushort uint16

type _Ctype_void [0]byte

//go:linkname _cgo_runtime_cgocall runtime.cgocall
func _cgo_runtime_cgocall(unsafe.Pointer, uintptr) int32

//go:linkname _cgoCheckPointer runtime.cgoCheckPointer
//go:noescape
func _cgoCheckPointer(interface{}, interface{})

//go:linkname _cgoCheckResult runtime.cgoCheckResult
//go:noescape
func _cgoCheckResult(interface{})
const _Ciconst_AF_INET = 0x2
const _Ciconst_AF_INET6 = 0xa
const _Ciconst_AF_UNSPEC = 0x0
const _Ciconst_AI_ALL = 0x10
const _Ciconst_AI_CANONNAME = 0x2
const _Ciconst_AI_V4MAPPED = 0x8
const _Ciconst_EAI_AGAIN = -0x3
const _Ciconst_EAI_NODATA = -0x5
const _Ciconst_EAI_NONAME = -0x2
const _Ciconst_EAI_OVERFLOW = -0xc
const _Ciconst_EAI_SERVICE = -0x8
const _Ciconst_EAI_SYSTEM = -0xb
const _Ciconst_IPPROTO_TCP = 0x6
const _Ciconst_IPPROTO_UDP = 0x11
const _Ciconst_NI_NAMEREQD = 0x8
const _Ciconst_SOCK_DGRAM = 0x2
const _Ciconst_SOCK_STREAM = 0x1

//go:cgo_import_static _cgo_97ab22c4dc7b_C2func_getaddrinfo
//go:linkname __cgofn__cgo_97ab22c4dc7b_C2func_getaddrinfo _cgo_97ab22c4dc7b_C2func_getaddrinfo
var __cgofn__cgo_97ab22c4dc7b_C2func_getaddrinfo byte
var _cgo_97ab22c4dc7b_C2func_getaddrinfo = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_C2func_getaddrinfo)

//go:cgo_unsafe_args
func _C2func_getaddrinfo(p0 *_Ctype_char, p1 *_Ctype_char, p2 *_Ctype_struct_addrinfo, p3 **_Ctype_struct_addrinfo) (r1 _Ctype_int, r2 error) {
	errno := _cgo_runtime_cgocall(_cgo_97ab22c4dc7b_C2func_getaddrinfo, uintptr(unsafe.Pointer(&p0)))
	if errno != 0 { r2 = syscall.Errno(errno) }
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_C2func_getnameinfo
//go:linkname __cgofn__cgo_97ab22c4dc7b_C2func_getnameinfo _cgo_97ab22c4dc7b_C2func_getnameinfo
var __cgofn__cgo_97ab22c4dc7b_C2func_getnameinfo byte
var _cgo_97ab22c4dc7b_C2func_getnameinfo = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_C2func_getnameinfo)

//go:cgo_unsafe_args
func _C2func_getnameinfo(p0 *_Ctype_struct_sockaddr, p1 _Ctype_socklen_t, p2 *_Ctype_char, p3 _Ctype_socklen_t, p4 *_Ctype_char, p5 _Ctype_socklen_t, p6 _Ctype_int) (r1 _Ctype_int, r2 error) {
	errno := _cgo_runtime_cgocall(_cgo_97ab22c4dc7b_C2func_getnameinfo, uintptr(unsafe.Pointer(&p0)))
	if errno != 0 { r2 = syscall.Errno(errno) }
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
		_Cgo_use(p5)
		_Cgo_use(p6)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_C2func_res_search
//go:linkname __cgofn__cgo_97ab22c4dc7b_C2func_res_search _cgo_97ab22c4dc7b_C2func_res_search
var __cgofn__cgo_97ab22c4dc7b_C2func_res_search byte
var _cgo_97ab22c4dc7b_C2func_res_search = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_C2func_res_search)

//go:cgo_unsafe_args
func _C2func_res_search(p0 *_Ctype_char, p1 _Ctype_int, p2 _Ctype_int, p3 *_Ctype_uchar, p4 _Ctype_int) (r1 _Ctype_int, r2 error) {
	errno := _cgo_runtime_cgocall(_cgo_97ab22c4dc7b_C2func_res_search, uintptr(unsafe.Pointer(&p0)))
	if errno != 0 { r2 = syscall.Errno(errno) }
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}

//go:linkname _cgo_runtime_gostring runtime.gostring
func _cgo_runtime_gostring(*_Ctype_char) string

// GoString converts the C string p into a Go string.
func _Cfunc_GoString(p *_Ctype_char) string {
	return _cgo_runtime_gostring(p)
}

func _Cfunc__CMalloc(n _Ctype_size_t) unsafe.Pointer {
	return _cgo_cmalloc(uint64(n))
}
//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc_free
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc_free _cgo_97ab22c4dc7b_Cfunc_free
var __cgofn__cgo_97ab22c4dc7b_Cfunc_free byte
var _cgo_97ab22c4dc7b_Cfunc_free = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc_free)

//go:cgo_unsafe_args
func _Cfunc_free(p0 unsafe.Pointer) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc_free, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc_freeaddrinfo
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc_freeaddrinfo _cgo_97ab22c4dc7b_Cfunc_freeaddrinfo
var __cgofn__cgo_97ab22c4dc7b_Cfunc_freeaddrinfo byte
var _cgo_97ab22c4dc7b_Cfunc_freeaddrinfo = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc_freeaddrinfo)

//go:cgo_unsafe_args
func _Cfunc_freeaddrinfo(p0 *_Ctype_struct_addrinfo) (r1 _Ctype_void) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc_freeaddrinfo, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc_gai_strerror
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc_gai_strerror _cgo_97ab22c4dc7b_Cfunc_gai_strerror
var __cgofn__cgo_97ab22c4dc7b_Cfunc_gai_strerror byte
var _cgo_97ab22c4dc7b_Cfunc_gai_strerror = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc_gai_strerror)

//go:cgo_unsafe_args
func _Cfunc_gai_strerror(p0 _Ctype_int) (r1 *_Ctype_char) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc_gai_strerror, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc_getaddrinfo
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc_getaddrinfo _cgo_97ab22c4dc7b_Cfunc_getaddrinfo
var __cgofn__cgo_97ab22c4dc7b_Cfunc_getaddrinfo byte
var _cgo_97ab22c4dc7b_Cfunc_getaddrinfo = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc_getaddrinfo)

//go:cgo_unsafe_args
func _Cfunc_getaddrinfo(p0 *_Ctype_char, p1 *_Ctype_char, p2 *_Ctype_struct_addrinfo, p3 **_Ctype_struct_addrinfo) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc_getaddrinfo, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc_getnameinfo
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc_getnameinfo _cgo_97ab22c4dc7b_Cfunc_getnameinfo
var __cgofn__cgo_97ab22c4dc7b_Cfunc_getnameinfo byte
var _cgo_97ab22c4dc7b_Cfunc_getnameinfo = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc_getnameinfo)

//go:cgo_unsafe_args
func _Cfunc_getnameinfo(p0 *_Ctype_struct_sockaddr, p1 _Ctype_socklen_t, p2 *_Ctype_char, p3 _Ctype_socklen_t, p4 *_Ctype_char, p5 _Ctype_socklen_t, p6 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc_getnameinfo, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
		_Cgo_use(p5)
		_Cgo_use(p6)
	}
	return
}
//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc_res_search
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc_res_search _cgo_97ab22c4dc7b_Cfunc_res_search
var __cgofn__cgo_97ab22c4dc7b_Cfunc_res_search byte
var _cgo_97ab22c4dc7b_Cfunc_res_search = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc_res_search)

//go:cgo_unsafe_args
func _Cfunc_res_search(p0 *_Ctype_char, p1 _Ctype_int, p2 _Ctype_int, p3 *_Ctype_uchar, p4 _Ctype_int) (r1 _Ctype_int) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc_res_search, uintptr(unsafe.Pointer(&p0)))
	if _Cgo_always_false {
		_Cgo_use(p0)
		_Cgo_use(p1)
		_Cgo_use(p2)
		_Cgo_use(p3)
		_Cgo_use(p4)
	}
	return
}

//go:cgo_import_static _cgo_97ab22c4dc7b_Cfunc__Cmalloc
//go:linkname __cgofn__cgo_97ab22c4dc7b_Cfunc__Cmalloc _cgo_97ab22c4dc7b_Cfunc__Cmalloc
var __cgofn__cgo_97ab22c4dc7b_Cfunc__Cmalloc byte
var _cgo_97ab22c4dc7b_Cfunc__Cmalloc = unsafe.Pointer(&__cgofn__cgo_97ab22c4dc7b_Cfunc__Cmalloc)

//go:linkname runtime_throw runtime.throw
func runtime_throw(string)

//go:cgo_unsafe_args
func _cgo_cmalloc(p0 uint64) (r1 unsafe.Pointer) {
	_cgo_runtime_cgocall(_cgo_97ab22c4dc7b_Cfunc__Cmalloc, uintptr(unsafe.Pointer(&p0)))
	if r1 == nil {
		runtime_throw("runtime: C malloc failed")
	}
	return
}
